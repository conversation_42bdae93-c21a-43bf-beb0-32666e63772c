# !/usr/bin/python3
# -*- coding: utf-8 -*-
import random
import string
import json

import weeeTest

from wms.test_dir.api.wms.wms import header


class CommonAPI(weeeTest.TestCase):
    """
    公共接口
    """

    def search_sys_config(self, config_key, warehouse_number):
        """获取系统配置"""
        url = "/wms/common/queryConfigList"
        body = {
            "warehouse_number": warehouse_number,
            "config_key": config_key
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def create_sys_config(self, config_key, config_value, warehouse_number):
        """新增系统配置"""
        url = "/wms/common/insertConfig"
        body = {
            "config_key": config_key,
            "config_value": config_value,
            "in_user": "wms_auto",
            "in_dtm": 1703239270,
            "warehouse_number": warehouse_number
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def update_sys_config(self, config_id, config_key, config_value, warehouse_number):
        """更新系统配置"""
        url = "/wms/common/updateConfig"
        body = {
            "config_key": config_key,
            "config_value": config_value,
            "desc": None,
            "edit_user": "wms_auto",
            "edit_dtm": 1703239503,
            "warehouse_number": warehouse_number,
            "rec_id": config_id
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def delete_sys_config(self, config_id):
        """删除系统配置"""
        url = "/wms/common/delConfig"
        body = {"rec_id": config_id}
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}删除系统配置接口请求失败,详情：{json.dumps(self.response)}"
        return self.response


    def record_moudle_log(self, warehouse_number, moudle_name):
        """记录用户进出模块日志"""
        url = "/wms/common/record/module/log"
        body = {
            "warehouse_number": warehouse_number,
            "module_name": moudle_name,
            "action": 1
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"

    def get_avaiable_bin(self, warehouse_number, storage_type=1):
        """获取可用Bin位"""
        url = "/wms/service/queryAvaiBinList"
        body = {
            "warehouse_number": warehouse_number,
            "storage_type": storage_type,
            "size_id": "",
            "location_no": "",
            "startColumn": 0,
            "pageSize": 10
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}获取可用Bin位接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]["data"]

    def get_avaiable_location(self, warehouse_number, location_type):
        """获取可用库位"""
        url = "/wms/location/query_location_info"
        body = {
            "warehouse_number": warehouse_number,
            "flag": "",
            "location_no": "",
            "location_types": [location_type],
            "storage_type": "",
            "restock_type": "",
            "size_id": "",
            "aisles": [],
            "bays": [],
            "locationAttributeCodes": [],
            "startColumn": 0,
            "pageSize": 15,
            "toteSizes": [],
            "tempZones": [],
            "levels": [],
            "positions": []
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]["data"]

    def create_new_location(self, warehouse_number, location_type, storage_type, tote_size, in_user):
        """创建新库位"""
        url = "/wms/location/insert_batch_location_info"
        random_str = ''.join(random.choices(string.ascii_lowercase, k=3))
        location_no = f"QA{random_str}{random.randint(1, 1000)}"
        body = {
            "in_user": in_user,
            "warehouse_number": warehouse_number,
            "location_nos": [location_no],
            "location_type": location_type,
            "storage_type": storage_type,
            "restock_type": "",
            "restockable": 1,
            "size_id": "",
            "tote_size": tote_size,
            "comment": "",
            "storage_temperate_zone": "",
            "locationAttributeCodes": [],
            "recommended_sku_count": None,
            "recommended_sku_layers": None
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]["data"]

    def query_item_info(self, warehouse_number, attributes, in_user, filter_column_rules, hasInventoryOnly="Y"):
        """获取商品基础信息和属性"""
        url = "/wms/items/management/queryItemsAttributeList"
        body = {
            "startColumn": 0,
            "pageSize": 5,
            "order": None,
            "warehouse_number": warehouse_number,
            "storage_types": [],
            "item_numbers": [],
            "category_ids": [],
            "status": "active",
            "hasInventoryOnly": "Y",
            "keyword": "",
            "viewDesc": "Custom",
            "attributes": attributes,
            "in_user": in_user,
            "filterColumnRules": filter_column_rules
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]["data"]