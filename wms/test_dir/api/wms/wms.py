# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  kaichuan.wang
@Version        :  V1.0.0
------------------------------------
@File           :  wms.py.py
@Description    :  
@CreateTime     :  2023/11/17 10:11
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/11/17 10:11
"""
import time

import weeeTest
from weeeTest import jmespath, log

from wms.qa_config import global_data
from wms.test_dir.api.wms import header
from wms.test_dir.api.wms.batch.batch import Batch
from wms.test_dir.api.wms.common.common_api import CommonAPI
from wms.test_dir.api.wms.cyclecount.count_api import Count
from wms.test_dir.api.wms.downorder.downorder import WmsDownOrder
from wms.test_dir.api.wms.downorder.downorder_job import DownOrderJOB
from wms.test_dir.api.wms.login import Login
from wms.test_dir.api.wms.packing.mof_packing import MofPacking
from wms.test_dir.api.wms.packing.normal_packing import NormalPacking
from wms.test_dir.api.wms.picking.alcohol_picking import AlcoholPicking
from wms.test_dir.api.wms.picking.bulk_picking import BulkPicking
from wms.test_dir.api.wms.picking.fbw_picking import FbwPicking
from wms.test_dir.api.wms.picking.mail_order_picking import MailOrderPicking
from wms.test_dir.api.wms.picking.mof_picking import MofPicking
from wms.test_dir.api.wms.picking.one_item_picking import OneItemPicking
from wms.test_dir.api.wms.packing.bulk_packing import BulkPacking
from wms.test_dir.api.wms.picking.picking import Picking
from wms.test_dir.api.wms.picking.auto_picking import AutoPickingApi
from wms.test_dir.api.wms.putaway.putaway_api import PutAwayAPI
from wms.test_dir.api.wms.receive.central_receive_api import CentralReceiveAPI
from wms.test_dir.api.wms.receive.label_receive_api import LabelReceiveAPI
from wms.test_dir.api.wms.replenish.replenish_api import ReplenishAPI
from wms.test_dir.api.wms.restock.restock_api import RestockAPI
from wms.test_dir.api.wms.routecheck.routecheck_api import RouteCheckAPI
from wms.test_dir.api.wms.repack.repack_api import Repack
from wms.test_dir.api.wms.adjust.adjust_api import Adjust
from wms.test_dir.api.wms.movingtool.moving_api import Moving
from wms.test_dir.api.wms.oos.oos_api import OrderOOSItem
from wms.test_dir.api.wms.vendor_return.returncentral_api import ReturnCentralAPI
from wms.test_dir.api.wms.vendor_return.returnpick_api import ReturnPickAPI
from wms.test_dir.api.wms.vendor_return.vendorpickup_api import VendorpickupAPI
from wms.test_dir.api.wms.vendor_return.commonpick_api import CommonPickAPI
from wms.test_dir.api.wms.global_fbw.globalfbw_api import GlobalFbwAPI
from tms.test_dir.api_case.tms.tms_sql import TmsDB
from wms.test_dir.api_case.wms.utils import DataUtils
from wms.test_dir.api_case.wms.wms_sql import WmsDB
from wms.test_dir.api_case.inv.inv_sql import InvDB
from wms.test_dir.api_case.wms.wms_assert import WmsAssert
from wms.test_dir.api.wms.transship.transship_api import TransshipAPI
from wms.test_dir.api.wms.expirationcheck.expiration_check_api import Expired
from wms.test_dir.api.wms.movingtool.collect_extra_api import CollectExtraAPI
from wms.test_dir.api.inv.ec_inventory_api import EcInventory
from wms.test_dir.api.inv.central_inventory_api import CentralInventory
from wms.test_dir.api.inv.ec_item_api import EcItem
from wms.test_dir.api.wms.receive.ro_receive_api import RoReceiveAPI
from wms.test_dir.api.wms.putaway.ro_putaway_api import RoPutAwayAPI
from tms.test_dir.api.tms.tms import tms



class WMS(weeeTest.TestCase):
    """
    WMS系统公共方法封装
    """
    wms_login = Login()
    down_order = WmsDownOrder()
    picking = Picking()
    bulk_packing = BulkPacking()
    mof_packing = MofPacking()
    normal_packing = NormalPacking()
    bulk_picking = BulkPicking()
    mail_order_picking = MailOrderPicking()
    mof_picking = MofPicking()
    one_item_picking = OneItemPicking()
    fbw_picking = FbwPicking()
    alcohol_picking = AlcoholPicking()
    autostore_picking = AutoPickingApi()
    replenish = ReplenishAPI()
    restock = RestockAPI()
    return_order = ReturnCentralAPI()
    return_pick = ReturnPickAPI()
    vendorpickup = VendorpickupAPI()
    common_pick = CommonPickAPI()
    globalfbw=GlobalFbwAPI()
    globalfbw = GlobalFbwAPI()
    route_check = RouteCheckAPI()
    down_order_job = DownOrderJOB()
    central_receive = CentralReceiveAPI()
    common_api = CommonAPI()
    adjust_api = Adjust()
    moving_api = Moving()
    put_away = PutAwayAPI()
    batch = Batch()
    count = Count()
    repack = Repack()
    wms_db = WmsDB()
    inv_db = InvDB()
    util = DataUtils()
    tms_db = TmsDB()
    wms_assert = WmsAssert()
    oos_api = OrderOOSItem()
    ts_api = TransshipAPI()
    expired_api = Expired()
    label_receive = LabelReceiveAPI()
    collect_extra_api = CollectExtraAPI()
    ec_inv_api = EcInventory()
    central_inv_api = CentralInventory()
    ec_item_api = EcItem()
    Ro_Receive = RoReceiveAPI()
    Ro_PutAway = RoPutAwayAPI()

    def login(self, account=global_data.wms_user_id, password=global_data.wms_user_password):
        """
        登录账户
        :param account:
        :param password:
        :return:
        """
        if not header["authorization"]:
            return self.wms_login.common_login(account, password)
        else:
            return global_data.wms_user_id, global_data.user_name

    def logout(self, warehouse_number, user_id, user_name):
        """
        退出登录
        :param warehouse_number:
        :param user_id:
        :param user_name:
        :return:
        """
        if header["authorization"]:
            self.wms_login.logout(warehouse_number, user_id, user_name)

    def get_central_config(self, warehouse_number, config_key):
        """
        查询central系统配置
        :param warehouse_number:
        :param config_key:
        :return:
        """
        ret = self.common_api.search_sys_config(config_key, warehouse_number)["body"]
        if len(ret) > 0:
            return ret[0]["config_value"]
        else:
            return None

    def update_central_config(self, warehouse_number, config_key, config_value):
        """
        更新central系统配置
        :param warehouse_number:
        :param config_key:
        :param config_value:
        :return:
        """
        ret = self.common_api.search_sys_config(config_key, warehouse_number)["body"]
        self.common_api.update_sys_config(ret[0]["rec_id"], config_key, config_value, warehouse_number)

    @staticmethod
    def select_order_storage_type(order_type):
        """
        根据order type获取在app和packing中对应作业的storage type
        """
        for storage_type, order_type_list in global_data.storage_type_mapping.items():
            if int(order_type) in order_type_list:
                return storage_type

    def down_order_process(self,order_id):
        # 订单处理
        self.tms_db.order_data_process(so_order_id=order_id)

        # 订单同步fpo
        self.down_order_job.put_order_from_so(so_order_id=order_id)

        # 获取config
        fpo_config_id = self.tms_db.get_order_fpo_config_info(order_id=order_id, info=['fpo_delivery_config_id'])[0][0]
        last_push_time = self.tms_db.get_last_push_time(config_id=fpo_config_id)

        # 调用订单下发job
        self.down_order_job.JobDownOrder(last_push_time=last_push_time)
        time.sleep(5)

        #获取订单仓库、类型、订单商品、订单商品tag


    def reset_data(self,delivery_id):
        # 重置WMS Data
        self.wms_db.clean_wms_data(delivery_id=delivery_id)

        # 重置FPO数据
        self.tms_db.clean_fpo_data(delivery_id=delivery_id)

        # 重置TMS数据
        sub_region_id = self.tms_db.get_sub_region_id_by_delivery(delivery_id=delivery_id)
        if sub_region_id:
            delivery_date = self.tms_db.get_delivery_date(delivery_id=delivery_id)
            tms.delete_delivery_data(delivery_date=delivery_date, sub_region_ids=sub_region_id)

        # 重置ERP数据
        self.tms_db.delete_delivery(delivery_id=delivery_id)

    def check_data(self,delivery_id):
        erp_data = self.tms_db.check_downorder_erp(delivery_id=delivery_id)
        erp_data_dicts = [
            {
                'invoice_no': str(entry[0]),
                'item_number': str(entry[1]),
                'itemQuantity': str(entry[2])
            } for entry in erp_data
        ]
        print("erp data dict", erp_data_dicts)

        wms_data = self.wms_db.check_downorder_wms(delivery_id=delivery_id)
        print("WMS DATA", wms_data)

        columns_to_compare = ['invoice_no', 'item_number', 'itemQuantity']

        for row1, row2 in zip(erp_data_dicts, wms_data):
            for column in columns_to_compare:
                if row1[column] != row2[column]:
                    print(f"不匹配的行：{row1} != {row2}")
                    return False

        return True

    def get_delivery_id(self,order_id):
        MAX_RETRIES = 3
        RETRY_DELAY = 5  # 秒
        for attempt in range(MAX_RETRIES):
            try:
                delivery_id_new = self.tms_db.get_delivery_id_by_order_id(order_id=order_id)
                if delivery_id_new:
                    return delivery_id_new
                else:
                    print(f"未查询到delivery_id, 订单ID: {order_id}")
            except IndexError:
                print(f"查询结果为空, 订单ID: {order_id}")

            if attempt < MAX_RETRIES - 1:
                print(f"等待{RETRY_DELAY}秒后重试...")
                time.sleep(RETRY_DELAY)

        print(f"在{MAX_RETRIES}次尝试后仍未查询到delivery_id")
        return None

    def picking_process(self, warehouse_number, storage_type, location_type=50, picking_channel=0):
        """
        拣货流程
        :param warehouse_number:
        :param location_type:50:normal_batch,mail_order,mof/51:alcohol/52:one_item/73:bulk/75:fbw/tote:6
        :param storage_type:1:常温,包含D,MO,酒类 2:冷藏,包含Fresh 3:冷冻Frozen 6:Multiple, Mof, 7:Frozen-Dry-Ice
        :param picking_channel:0:picking 1:mailOrder 2:alcohol
        :return:
        """
        location_no = self.wms_db.get_wh_storage_location_info(warehouse=warehouse_number, location_type=location_type,
                                                               info='location_no')
        resp = self.picking.create_picking_task(cart_no=location_no, warehouse_number=warehouse_number,
                                                storage_type=storage_type, picking_channel=picking_channel)
        if resp:
            box_size = jmespath(resp, "body.box_size")
            picking_task_id = jmespath(resp, "body.picking_task_id")
            order_ids = jmespath(resp, "body.picking_order[*].order_id")
            shipping_types = jmespath(resp, "body.picking_order[*].shipping_type")
            # 查询可用的tote
            tote_nos = self.wms_db.select_available_tote(location_type=6, flag=0, warehouse=warehouse_number,
                                                         box_size=box_size)
            picking_totes = []
            # 订单绑定拣货框
            for i in range(len(order_ids)):
                resp = self.picking.bind_tote_number(picking_task_id=picking_task_id,
                                                     tote_no=tote_nos[i]["location_no"],
                                                     order_id=order_ids[i],
                                                     shipping_type=shipping_types[i],
                                                     warehouse_number=warehouse_number)

            #  库存调整
            while True:
                next_item = jmespath(resp, "body.nextItem")
                if next_item is None:
                    break
                picking_totes.append(next_item["tote_no"])
                resp = self.picking.adjust_location(warehouse_number=warehouse_number,
                                                    tote_no=next_item["tote_no"],
                                                    location_no=next_item["location_no"],
                                                    item_number=next_item["item_number"],
                                                    order_id=next_item["order_id"],
                                                    picking_quantity=next_item["item_quantity"],
                                                    picking_type=jmespath(resp, "body.picking_type"),
                                                    picking_task_id=jmespath(resp, "body.picking_task_id"))

            # 结束拣货任务
            self.picking.picking_task_finish(picking_task_id=jmespath(resp, "body.picking_task_id"),
                                             picking_type=jmespath(resp, "body.picking_type"),
                                             packing_line=jmespath(resp, "body.packing_line"),
                                             warehouse_number=warehouse_number,
                                             picking_task_status=jmespath(resp, "body.status"))
            picking_totes = list(set(picking_totes))
            log.info(f"picking_totes:{picking_totes}")
            return picking_totes

    def mof_picking_process(self, warehouse_number):
        """
        mof拣货流程
        """
        location_no = self.wms_db.get_wh_storage_location_info(warehouse=warehouse_number, location_type=50, flag=0,
                                                               info='location_no')
        # 创建拣货任务
        resp = self.mof_picking.create_picking_task(cart_no=location_no, warehouse_number=warehouse_number)

        box_size = jmespath(resp, "body.box_size")
        picking_task_id = jmespath(resp, "body.picking_task_id")
        order_ids = jmespath(resp, "body.picking_order[*].order_id")
        shipping_types = jmespath(resp, "body.picking_order[*].shipping_type")

        # 查询可用的tote
        tote_nos = self.wms_db.select_available_tote(location_type=6, flag=0, warehouse=warehouse_number,
                                                     box_size=box_size)

        # 判断订单与数据库中查询的tote数量
        total_order_num = len(order_ids)
        total_shipping_num = len(shipping_types)
        assert total_order_num == total_shipping_num
        assert len(tote_nos) >= total_order_num

        # 订单绑定拣货框
        print("共" + str(total_order_num) + "个订单需要绑定拣货框")
        for i in range(total_order_num):
            resp = self.mof_picking.bind_tote_number(picking_task_id=picking_task_id,
                                                     tote_no=tote_nos[i]["location_no"],
                                                     order_id=order_ids[i],
                                                     shipping_type=shipping_types[i],
                                                     warehouse_number=warehouse_number)

            #  库存调整
            while True:
                next_item = jmespath(resp, "body.nextItem")
                picking_region_no = jmespath(resp, "body.picking_region_no")
                if next_item is None:
                    print("没有生鲜作业，扫结束码后，重新扫描cart作业干货")
                    break
                # 不是null，则表示有生鲜冻货作业，继续拣货
                resp = self.mof_picking.adjust_location(warehouse_number=warehouse_number,
                                                        tote_no=next_item["tote_no"],
                                                        location_no=next_item["location_no"],
                                                        item_number=next_item["item_number"],
                                                        order_id=next_item["order_id"],
                                                        picking_quantity=next_item["item_quantity"],
                                                        picking_type=jmespath(resp, "body.picking_type"),
                                                        picking_task_id=jmespath(resp, "body.picking_task_id"))

            # 结束生鲜拣货任务
            self.mof_picking.scan_picking_region(picking_region_no=picking_region_no,
                                                 picking_task_id=picking_task_id, warehouse_number=warehouse_number)

            # 再扫cart，继续剩下的作业
            resp = self.mof_picking.create_picking_task(cart_no=location_no, warehouse_number=warehouse_number)
            print("再次扫cart，继续剩下的作业")

            while True:
                next_item = jmespath(resp, "body.nextItem")
                picking_region_no = jmespath(resp, "body.picking_region_no")
                if next_item is None:
                    print("生鲜作业也完成，扫描最终打包线")
                    break

                resp = self.mof_picking.adjust_location(warehouse_number=warehouse_number,
                                                        tote_no=next_item["tote_no"],
                                                        location_no=next_item["location_no"],
                                                        item_number=next_item["item_number"],
                                                        order_id=next_item["order_id"],
                                                        picking_quantity=next_item["item_quantity"],
                                                        picking_type=jmespath(resp, "body.picking_type"),
                                                        picking_task_id=jmespath(resp, "body.picking_task_id"))
            # 结束任务
            self.mof_picking.picking_task_finish(picking_task_id=jmespath(resp, "body.picking_task_id"),
                                                 picking_type=jmespath(resp, "body.picking_type"),
                                                 packing_line=picking_region_no,
                                                 warehouse_number=warehouse_number)

    def mo_picking_process(self, warehouse_number):
        """
        mo拣货流程
        :param warehouse_number:
        :return:
        """
        location_no = self.wms_db.get_wh_storage_location_info(warehouse=warehouse_number, location_type=50, flag=0,
                                                               info='location_no')

        # 选择picking模块
        self.mail_order_picking.enter_module(warehouse_number=warehouse_number)

        # 创建拣货任务
        self.mail_order_picking.create_picking_task(cart_no=location_no, warehouse_number=warehouse_number)
        resp = self.mail_order_picking.create_picking_task(cart_no=location_no, warehouse_number=warehouse_number)

        box_size = jmespath(resp, "body.box_size")
        picking_task_id = jmespath(resp, "body.picking_task_id")
        order_ids = jmespath(resp, "body.picking_order[*].order_id")
        shipping_types = jmespath(resp, "body.picking_order[*].shipping_type")

        # 查询可用的tote
        tote_nos = self.wms_db.select_available_tote(location_type=6, flag=0, warehouse=warehouse_number,
                                                     box_size=box_size)

        # 判断订单与数据库中查询的tote数量
        total_order_num = len(order_ids)
        total_shipping_num = len(shipping_types)
        assert total_order_num == total_shipping_num
        assert len(tote_nos) >= total_order_num

        # 订单绑定拣货框
        print("共" + str(total_order_num) + "个订单需要绑定拣货框")
        for i in range(total_order_num):
            resp = self.mail_order_picking.bind_tote_number(picking_task_id=picking_task_id,
                                                            tote_no=tote_nos[i]["location_no"],
                                                            order_id=order_ids[i],
                                                            shipping_type=shipping_types[i],
                                                            warehouse_number=warehouse_number)

            #  库存调整
            while True:
                next_item = jmespath(resp, "body.nextItem")
                if next_item is None:
                    break

                resp = self.mail_order_picking.adjust_location(warehouse_number=warehouse_number,
                                                               tote_no=next_item["tote_no"],
                                                               location_no=next_item["location_no"],
                                                               item_number=next_item["item_number"],
                                                               order_id=next_item["order_id"],
                                                               picking_quantity=next_item["item_quantity"],
                                                               picking_type=jmespath(resp, "body.picking_type"),
                                                               picking_task_id=jmespath(resp,
                                                                                        "body.picking_task_id"))

            # 结束拣货任务
            self.mail_order_picking.picking_task_finish(picking_task_id=jmespath(resp, "body.picking_task_id"),
                                                        picking_type=jmespath(resp, "body.picking_type"),
                                                        packing_line=jmespath(resp, "body.packing_line"),
                                                        warehouse_number=warehouse_number)

    def auto_picking_process(self, warehouse_number, storage_type, location_type=50, picking_channel=0):
        """
        拣货流程(仅一键出库脚本使用,请勿增加相关校验)
        :param warehouse_number:
        :param location_type:50:normal_batch,mail_order,mof/51:alcohol/52:one_item/73:bulk/75:fbw/tote:6
        :param storage_type:1:常温,包含D,MO,酒类 2:冷藏,包含Fresh 3:冷冻Frozen 6:Multiple, Mof, 7:Frozen-Dry-Ice
        :param picking_channel:0:picking 1:mailOrder 2:alcohol
        :return:
        """
        location_no = self.wms_db.get_wh_storage_location_info(warehouse=warehouse_number, location_type=location_type,
                                                               info='location_no')
        resp = self.picking.create_picking_task(cart_no=location_no, warehouse_number=warehouse_number,
                                                storage_type=storage_type, picking_channel=picking_channel)
        if resp:
            box_size = jmespath(resp, "body.box_size")
            picking_task_id = jmespath(resp, "body.picking_task_id")
            order_ids = jmespath(resp, "body.picking_order[*].order_id")
            shipping_types = jmespath(resp, "body.picking_order[*].shipping_type")
            # 查询可用的tote
            tote_nos = self.wms_db.select_available_tote(location_type=6, flag=0, warehouse=warehouse_number,
                                                         box_size=box_size)
            picking_totes = []
            # 订单绑定拣货框
            for i in range(len(order_ids)):
                resp = self.picking.bind_tote_number(picking_task_id=picking_task_id,
                                                     tote_no=tote_nos[i]["location_no"],
                                                     order_id=order_ids[i],
                                                     shipping_type=shipping_types[i],
                                                     warehouse_number=warehouse_number)

            #  库存调整
            while True:
                next_item = jmespath(resp, "body.nextItem")
                if next_item is None:
                    break
                picking_totes.append(next_item["tote_no"])
                resp = self.picking.adjust_location(warehouse_number=warehouse_number,
                                                    tote_no=next_item["tote_no"],
                                                    location_no=next_item["location_no"],
                                                    item_number=next_item["item_number"],
                                                    order_id=next_item["order_id"],
                                                    picking_quantity=next_item["item_quantity"],
                                                    picking_type=jmespath(resp, "body.picking_type"),
                                                    picking_task_id=jmespath(resp, "body.picking_task_id"))

            # 结束拣货任务
            self.picking.picking_task_finish(picking_task_id=jmespath(resp, "body.picking_task_id"),
                                             picking_type=jmespath(resp, "body.picking_type"),
                                             packing_line=jmespath(resp, "body.packing_line"),
                                             warehouse_number=warehouse_number,
                                             picking_task_status=jmespath(resp, "body.status"))
            picking_totes = list(set(picking_totes))
            log.info(f"picking_totes:{picking_totes}")
            return picking_totes

    def auto_mof_picking_process(self, warehouse_number):
        """
        mof拣货流程(仅一键出库脚本使用,请勿增加相关校验)
        """
        location_no = self.wms_db.get_wh_storage_location_info(warehouse=warehouse_number, location_type=50, flag=0,
                                                               info='location_no')
        # 创建拣货任务
        resp = self.mof_picking.create_picking_task(cart_no=location_no, warehouse_number=warehouse_number)

        box_size = jmespath(resp, "body.box_size")
        picking_task_id = jmespath(resp, "body.picking_task_id")
        order_ids = jmespath(resp, "body.picking_order[*].order_id")
        shipping_types = jmespath(resp, "body.picking_order[*].shipping_type")

        # 查询可用的tote
        tote_nos = self.wms_db.select_available_tote(location_type=6, flag=0, warehouse=warehouse_number,
                                                     box_size=box_size)

        # 判断订单与数据库中查询的tote数量
        total_order_num = len(order_ids)
        total_shipping_num = len(shipping_types)
        assert total_order_num == total_shipping_num
        assert len(tote_nos) >= total_order_num

        # 订单绑定拣货框
        print("共" + str(total_order_num) + "个订单需要绑定拣货框")
        for i in range(total_order_num):
            resp = self.mof_picking.bind_tote_number(picking_task_id=picking_task_id,
                                                     tote_no=tote_nos[i]["location_no"],
                                                     order_id=order_ids[i],
                                                     shipping_type=shipping_types[i],
                                                     warehouse_number=warehouse_number)

            #  库存调整
            while True:
                next_item = jmespath(resp, "body.nextItem")
                picking_region_no = jmespath(resp, "body.picking_region_no")
                if next_item is None:
                    print("没有生鲜作业，扫结束码后，重新扫描cart作业干货")
                    break
                # 不是null，则表示有生鲜冻货作业，继续拣货
                resp = self.mof_picking.adjust_location(warehouse_number=warehouse_number,
                                                        tote_no=next_item["tote_no"],
                                                        location_no=next_item["location_no"],
                                                        item_number=next_item["item_number"],
                                                        order_id=next_item["order_id"],
                                                        picking_quantity=next_item["item_quantity"],
                                                        picking_type=jmespath(resp, "body.picking_type"),
                                                        picking_task_id=jmespath(resp, "body.picking_task_id"))

            # 结束生鲜拣货任务
            self.mof_picking.scan_picking_region(picking_region_no=picking_region_no,
                                                 picking_task_id=picking_task_id, warehouse_number=warehouse_number)

            # 再扫cart，继续剩下的作业
            resp = self.mof_picking.create_picking_task(cart_no=location_no, warehouse_number=warehouse_number)
            print("再次扫cart，继续剩下的作业")

            while True:
                next_item = jmespath(resp, "body.nextItem")
                picking_region_no = jmespath(resp, "body.picking_region_no")
                if next_item is None:
                    print("生鲜作业也完成，扫描最终打包线")
                    break

                resp = self.mof_picking.adjust_location(warehouse_number=warehouse_number,
                                                        tote_no=next_item["tote_no"],
                                                        location_no=next_item["location_no"],
                                                        item_number=next_item["item_number"],
                                                        order_id=next_item["order_id"],
                                                        picking_quantity=next_item["item_quantity"],
                                                        picking_type=jmespath(resp, "body.picking_type"),
                                                        picking_task_id=jmespath(resp, "body.picking_task_id"))
            # 结束任务
            self.mof_picking.picking_task_finish(picking_task_id=jmespath(resp, "body.picking_task_id"),
                                                 picking_type=jmespath(resp, "body.picking_type"),
                                                 packing_line=picking_region_no,
                                                 warehouse_number=warehouse_number)

    def auto_mo_picking_process(self, warehouse_number):
        """
        mo拣货流程(仅一键出库脚本使用,请勿增加相关校验)
        :param warehouse_number:
        :return:
        """
        location_no = self.wms_db.get_wh_storage_location_info(warehouse=warehouse_number, location_type=50, flag=0,
                                                               info='location_no')

        # 选择picking模块
        self.mail_order_picking.enter_module(warehouse_number=warehouse_number)

        # 创建拣货任务
        self.mail_order_picking.create_picking_task(cart_no=location_no, warehouse_number=warehouse_number)
        resp = self.mail_order_picking.create_picking_task(cart_no=location_no, warehouse_number=warehouse_number)

        box_size = jmespath(resp, "body.box_size")
        picking_task_id = jmespath(resp, "body.picking_task_id")
        order_ids = jmespath(resp, "body.picking_order[*].order_id")
        shipping_types = jmespath(resp, "body.picking_order[*].shipping_type")

        # 查询可用的tote
        tote_nos = self.wms_db.select_available_tote(location_type=6, flag=0, warehouse=warehouse_number,
                                                     box_size=box_size)

        # 判断订单与数据库中查询的tote数量
        total_order_num = len(order_ids)
        total_shipping_num = len(shipping_types)
        assert total_order_num == total_shipping_num
        assert len(tote_nos) >= total_order_num

        # 订单绑定拣货框
        print("共" + str(total_order_num) + "个订单需要绑定拣货框")
        for i in range(total_order_num):
            resp = self.mail_order_picking.bind_tote_number(picking_task_id=picking_task_id,
                                                            tote_no=tote_nos[i]["location_no"],
                                                            order_id=order_ids[i],
                                                            shipping_type=shipping_types[i],
                                                            warehouse_number=warehouse_number)

            #  库存调整
            while True:
                next_item = jmespath(resp, "body.nextItem")
                if next_item is None:
                    break

                resp = self.mail_order_picking.adjust_location(warehouse_number=warehouse_number,
                                                               tote_no=next_item["tote_no"],
                                                               location_no=next_item["location_no"],
                                                               item_number=next_item["item_number"],
                                                               order_id=next_item["order_id"],
                                                               picking_quantity=next_item["item_quantity"],
                                                               picking_type=jmespath(resp, "body.picking_type"),
                                                               picking_task_id=jmespath(resp,
                                                                                        "body.picking_task_id"))

            # 结束拣货任务
            self.mail_order_picking.picking_task_finish(picking_task_id=jmespath(resp, "body.picking_task_id"),
                                                        picking_type=jmespath(resp, "body.picking_type"),
                                                        packing_line=jmespath(resp, "body.packing_line"),
                                                        warehouse_number=warehouse_number)

    def normal_packing_operation(self, warehouse, tote_no, order_id, storage_type, account=global_data.wms_user_id,
                                 username=global_data.user_name, is_oos=False, is_replenish=False, is_mod=False,
                                 is_cancel=False):
        """
        普通单打包
        :param order_id: wms系统的order_id
        :param warehouse:
        :param tote_no:
        :param storage_type:
        :param account:
        :param username:
        :param is_oos: 转补单,缺发出库
        :param is_replenish: 转补单
        :param is_mod: MO-Dry流程
        :param is_cancel: Move To COT流程
        :return:
        """
        # 设置仓库
        self.util.update_header(weee_warehouse=str(warehouse))
        # 设置storage_type
        self.util.update_header(weee_wms_storage_type=str(storage_type))

        # 开始打包
        self.normal_packing.user = username + '(' + account + ')'
        self.normal_packing.warehouse = warehouse

        # 设置打包台号，直邮、酒等订单对打包台号有特殊要求
        station = "1-1"
        if is_mod:
            mo_prefix_config = self.wms_db.select_wh_config_by_key(warehouse, "wms:downorder:mail_order_wave")
            carrier = self.wms_db.select_wms_order_info(order_id)["shipping_name"]
            for config in mo_prefix_config:
                if carrier == config["carrier"]:
                    station = config["prefix"] + "1"
                    break
        # 获取打包任务类型,更新header
        resp = self.normal_packing.query_packing_task(tote_no, station)
        self.util.update_header(weee_wms_packing_type=str(weeeTest.jmespath(resp, "body.packing_type")))
        items, order_id, recommend_package = self.normal_packing.query_packing_info(tote_no, station)
        oos_item = {}

        # 获取操作时间
        in_dtm = self.util.get_current_time()

        # check batch
        is_batch = self.get_inventory_batch_switch(warehouse, storage_type)

        # 当打包Tote/cart中库存数据异常时，恢复库存
        if not wms.adjust_api.query_location_inv_list(warehouse, tote_no):
            for item in items:
                wms.adjust_api.create_batch_inv(item["item_number"], warehouse, username + '(' + account + ')', tote_no,
                                                item["item_quantity"])
                if is_batch:
                    item["batch_no"] = \
                    self.wms_db.get_batch_inventory_transaction(warehouse, tote_no, item["item_number"])["batch_no"]

        if is_cancel:
            self.normal_packing.move_to_cot(tote_no, order_id)
            self.normal_packing.create_cot_label(order_id)
            return is_batch, items, in_dtm

        recommend_box = list(map(lambda i: i["barcode"], recommend_package))
        # Fresh、Frozen订单包材需要保温袋和冰
        if str(storage_type) != "1":
            recommend_box.extend(["MB22", "MB19"])
            recommend_box = list(set(recommend_box))

        # 校验订单状态(shipping_status=60,61)
        # self.wms_db.check_shipping_status(order_id=order_id, status=60)
        shipping_status = self.wms_db.check_shipping_status(order_id=order_id)
        assert shipping_status in (60, 61), "未更新wh_order_info表的shipping_status为开始打包状态"

        # 若需要转补单，QC后结束
        if is_replenish:
            oos_item = self.normal_packing.oos_qc(items, order_id)
            return oos_item, is_batch, items, in_dtm
        # 缺发出库
        if is_oos:
            oos_item = self.normal_packing.oos_qc(items, order_id)
            self.normal_packing.packing_force_stock(tote_no, order_id)
            status = 71
        # 正常出库
        else:
            self.normal_packing.packing_qc(items, order_id)
            # 检查订单下商品是否全部QC
            self.wms_db.check_all_qc(order_id=order_id)
            status = 70

        self.normal_packing.scan_box(recommend_box, order_id)

        # 若订单是 Mail order Dry
        if is_mod:
            self.normal_packing.mo_label_create(order_id)
        else:
            self.normal_packing.label_create(order_id)

        self.normal_packing.normal_ship(tote_no, order_id)
        # check order status
        self.wms_db.check_shipping_status(order_id=order_id, status=status)
        # check tote status
        self.wms_db.check_tote_status(tote_no=tote_no, warehouse=warehouse, status=0)

        # check inventory
        for item in items:
            if item["item_number"] != oos_item.get("item_number", ""):
                self.wms_assert.check_non_batch_invenotry(warehouse, tote_no, item["item_number"], -item["quantity"],
                                                          reference_no=order_id, inventory_type=[261], in_dtm=in_dtm)
                if is_batch:
                    self.wms_assert.check_batch_invenotry(warehouse, tote_no, item["item_number"], -item["quantity"],
                                                          batch_no=item["batch_no"], reference_no=order_id,
                                                          inventory_type=[261], in_dtm=in_dtm)
            else:
                self.wms_assert.check_non_batch_invenotry(warehouse, tote_no, item["item_number"], -item["quantity"],
                                                          reference_no=order_id, inventory_type=[261, 801],
                                                          in_dtm=in_dtm)
                if is_batch:
                    self.wms_assert.check_batch_invenotry(warehouse, tote_no, item["item_number"], -item["quantity"],
                                                          batch_no=item["batch_no"], reference_no=order_id,
                                                          inventory_type=[261, 801],
                                                          in_dtm=in_dtm)

        order_info = self.wms_db.select_wms_order_info(order_id)
        # check report TMS tracking number, MO 不通知TMS
        if not is_mod:
            self.wms_assert.check_hook_report(order_info["source_order_id"], "TMS_PACKAGE", in_dtm)
        else:
            # 需求变更，直邮打包完成不再上报SO 物流单号，Route check完成后上报
            pass
            # check report SO tracking number
            # order_package = self.wms_db.get_track_num(order_id)
            # self.wms_assert.check_mq_report(warehouse, order_package[0]["tracking_num"])

        # check report FPO
        invoice_no = order_info["invoice_no"]
        order_list = self.wms_db.get_invoice_order(invoice_no)
        is_markout = True
        for order in order_list:
            if order["shipping_status"] < 70 and order["order_status"] == 0:
                is_markout = False
        if is_markout:
            self.wms_assert.check_hook_report(invoice_no, "NOTIFY_2WMS_ORDER_MARK_OUT", in_dtm)

        if is_oos:
            self.wms_assert.check_hook_report(order_info["source_order_id"], "ITEM_OOS_FPO", in_dtm)

    def auto_normal_packing_operation(self, warehouse, tote_no, order_id, storage_type, account=global_data.wms_user_id,
                                      username=global_data.user_name, is_mod=False):
        """
        普通单脚本自动打包(仅一键出库脚本使用,请勿增加相关校验)
        :param order_id: wms系统的order_id
        :param warehouse:
        :param tote_no:
        :param storage_type:
        :param account:
        :param username:
        :param is_mod: MO-Dry流程
        :return:
        """
        # 设置仓库
        self.util.update_header(weee_warehouse=str(warehouse))
        # 设置storage_type
        self.util.update_header(weee_wms_storage_type=str(storage_type))

        # 开始打包
        self.normal_packing.user = username + '(' + account + ')'
        self.normal_packing.warehouse = warehouse

        # 设置打包台号，直邮、酒等订单对打包台号有特殊要求
        station = "1-1"
        if is_mod:
            mo_prefix_config = self.wms_db.select_wh_config_by_key(warehouse, "wms:downorder:mail_order_wave")
            carrier = self.wms_db.select_wms_order_info(order_id)["shipping_name"]
            for config in mo_prefix_config:
                if carrier == config["carrier"]:
                    station = config["prefix"] + "1"
                    break
        # 获取打包任务类型,更新header
        resp = self.normal_packing.query_packing_task(tote_no, station)
        self.util.update_header(weee_wms_packing_type=str(weeeTest.jmespath(resp, "body.packing_type")))
        items, order_id, recommend_package = self.normal_packing.query_packing_info(tote_no, station)

        # check batch
        is_batch = self.get_inventory_batch_switch(warehouse, storage_type)
        print(f'is_batch:{is_batch}')

        # 当打包Tote/cart中库存数据异常时，恢复库存
        if not wms.adjust_api.query_location_inv_list(warehouse, tote_no):
            for item in items:
                wms.adjust_api.create_batch_inv(item["item_number"], warehouse, username + '(' + account + ')', tote_no,
                                                item["item_quantity"])
                if is_batch:
                    item["batch_no"] = \
                        self.wms_db.get_batch_inventory_transaction(warehouse, tote_no, item["item_number"])["batch_no"]

        recommend_box = list(map(lambda i: i["barcode"], recommend_package))
        # Fresh、Frozen订单包材需要保温袋和冰
        if str(storage_type) != "1":
            recommend_box.extend(["MB22", "MB19"])
            recommend_box = list(set(recommend_box))
        # 正常出库
        self.normal_packing.packing_qc(items, order_id)
        # 检查订单下商品是否全部QC
        self.wms_db.check_all_qc(order_id=order_id)

        self.normal_packing.scan_box(recommend_box, order_id)

        # 若订单是 Mail order Dry
        if is_mod:
            self.normal_packing.mo_label_create(order_id)
        else:
            self.normal_packing.label_create(order_id)

        self.normal_packing.normal_ship(tote_no, order_id)
        # check order status
        self.wms_db.check_shipping_status(order_id=order_id, status=70)
        # check tote status
        self.wms_db.check_tote_status(tote_no=tote_no, warehouse=warehouse, status=0)

    def mof_packing_operation(self, warehouse, tote_no, order_id, storage_type, account=global_data.wms_user_id,
                              username=global_data.user_name, is_cancel=False, is_oos=False, is_replenish=False):
        """
        mail order fresh 打包出库
        :param warehouse:
        :param order_id:
        :param tote_no:
        :param storage_type:
        :param account:
        :param username:
        :return:
        """
        # 设置仓库
        self.util.update_header(weee_warehouse=str(warehouse))
        # 设置MOF storage_type
        self.util.update_header(weee_wms_storage_type=str(storage_type))
        # 开始打包
        self.mof_packing.user = username + '(' + account + ')'
        self.mof_packing.warehouse = warehouse

        resp = self.mof_packing.query_packing_task(tote_no)
        self.util.update_header(weee_wms_packing_type=str(jmespath(resp, "body.packing_type")))
        items, order_id, recommend_package = self.mof_packing.query_mof_packing_info(tote_no)
        oos_item = {}

        # 获取操作时间
        in_dtm = self.util.get_current_time()

        # 当打包Tote/cart中库存数据异常时，恢复库存
        if not wms.adjust_api.query_location_inv_list(warehouse, tote_no):
            for item in items:
                storage_type = self.wms_db.get_item_info(item["item_number"])["storage_type"]
                is_batch = self.get_inventory_batch_switch(warehouse, storage_type)
                item["is_batch"] = is_batch
                wms.adjust_api.create_batch_inv(item["item_number"], warehouse, username + '(' + account + ')', tote_no,
                                                item["item_quantity"])
                if is_batch:
                    item["batch_no"] = \
                        self.wms_db.get_batch_inventory_transaction(warehouse, tote_no, item["item_number"])["batch_no"]

        if is_cancel:
            self.mof_packing.mof_move_to_cot(tote_no, order_id)
            self.mof_packing.mof_create_cot_label(order_id)
            return items, in_dtm

        # 若需要转补单，QC后结束
        if is_replenish:
            oos_item = items
            return oos_item, is_batch, items, in_dtm

        # 含有Fresh、Frozen订单包材需要保温袋和冰
        for item in items:
            if item["storage_type"] == 2 or item["storage_type"] == 3:
                recommend_package.extend([{"barcode": "MB22", "type": 3}, {"barcode": "MB20", "type": 3}])
                break

        self.mof_packing.mix_scan_box(recommend_package, order_id)

        # 缺发出库
        if is_oos:
            oos_item = self.mof_packing.mix_oos_qc(items, order_id)
            self.mof_packing.box_full(order_id)
            self.mof_packing.mix_packing_force_stock(tote_no, order_id)
            status = 71
        # 正常出库
        else:
            self.mof_packing.mix_packing_qc(items, order_id)
            self.mof_packing.box_full(order_id)
            status = 70
        self.mof_packing.mof_label_create(order_id)
        self.mof_packing.mof_ship(tote_no, order_id)
        # check order status
        self.wms_db.check_shipping_status(order_id=order_id, status=status)
        # check tote status
        self.wms_db.check_tote_status(tote_no=tote_no, warehouse=warehouse, status=0)
        # check inventory
        for item in items:
            if item["item_number"] != oos_item.get("item_number", ""):
                self.wms_assert.check_non_batch_invenotry(warehouse, tote_no, item["item_number"], -item["quantity"],
                                                          reference_no=order_id, inventory_type=[261], in_dtm=in_dtm)
                if is_batch:
                    self.wms_assert.check_batch_invenotry(warehouse, tote_no, item["item_number"], -item["quantity"],
                                                          batch_no=item["batch_no"], reference_no=order_id,
                                                          inventory_type=[261], in_dtm=in_dtm)
            else:
                self.wms_assert.check_non_batch_invenotry(warehouse, tote_no, item["item_number"], -item["quantity"],
                                                          reference_no=order_id, inventory_type=[261, 801],
                                                          in_dtm=in_dtm)
                if is_batch:
                    self.wms_assert.check_batch_invenotry(warehouse, tote_no, item["item_number"], -item["quantity"],
                                                          batch_no=item["batch_no"], reference_no=order_id,
                                                          inventory_type=[261, 801], in_dtm=in_dtm)

        # check report SO tracking number  需求变更 此处不再上报so
        # order_package = self.wms_db.get_track_num(order_id)
        # self.wms_assert.check_mq_report(warehouse, order_package[0]["tracking_num"])

        # check report FPO
        # order_info = self.wms_db.select_wms_order_info(order_id)
        # invoice_no = order_info["invoice_no"]
        # order_list = self.wms_db.get_invoice_order(invoice_no)
        # is_markout = True
        # for order in order_list:
        #     if order["shipping_status"] < 70 and order["order_status"] == 0:
        #         is_markout = False
        # if is_markout:
        #     self.wms_assert.check_hook_report(invoice_no, "NOTIFY_2WMS_ORDER_MARK_OUT", in_dtm)
        # if is_oos:
        #     self.wms_assert.check_hook_report(order_info["source_order_id"], "ITEM_OOS_FPO", in_dtm)

    def fbw_cut_off(self, delivery_date, region_id, warehouse_no):
        """
        FBW截单
        :param delivery_date:
        :param region_id:
        :param warehouse_no:
        :return:
        """
        if region_id in (5, 6, 7, 8):
            self.down_order_job.cutoff_config(delivery_date=delivery_date, region_id=48, warehouse_no=37)
        else:
            fbw_region_id = self.tms_db.get_fbw_region_id(region_id=region_id)
            if fbw_region_id:
                self.down_order_job.cutoff_config(delivery_date=delivery_date, region_id=fbw_region_id,
                                                  warehouse_no=warehouse_no)

    def route_check_operation(self, warehouse, delivery_dtm, route_id, order_type, account=global_data.wms_user_id,
                              username=global_data.user_name):
        # route check 接口
        wms.route_check.user = username + '(' + str(account) + ')'
        storage_type = self.select_order_storage_type(order_type)

        # 获取route下的订单数据
        order_data = wms.route_check.get_route_package(warehouse, delivery_dtm, route_id, storage_type)
        order_id = None
        for order in order_data:
            if order["confirm_package_number"] < order["package_number"] and order["shipping_status"] in [70, 71]:
                order_id = order["order_id"]
                break
        if not order_id:
            return
        # 获取待route check 订单下包裹的tracking number
        ret = wms.wms_db.get_track_num(order_id=order_id)
        tracking_list = list(map(lambda i: i["tracking_num"], ret))

        # 依次scan 订单下包裹的tracking number
        for tracking_num in tracking_list:
            wms.route_check.package_confirm(warehouse, delivery_dtm, route_id, tracking_num, storage_type)

        # 检查订单下包裹route check的情况
        ret = wms.wms_db.get_wh_packing_order_info(order_id=order_id)
        assert ret["confirm_package_number"] == ret["package_number"]

    def change_sys_date(self, warehouse_numbers: list, delivery_date: str):
        """
        修改系统时间
        :param warehouse_numbers:
        :param delivery_date:
        :return:
        """
        config_keys = ["wms:warehouse:sys_date", "wms:order:delivery_date"]
        for key in config_keys:
            for wh in warehouse_numbers:
                rec_id = self.wms_db.get_wh_config_date(warehouse_number=wh, key=key)
                self.batch.update_config(config_key=key, config_value=delivery_date, warehouse_number=wh, rec_id=rec_id)

    def update_wh_config_picking_check(self, warehouse_number, picking_check='false', key="wms:routeInfoCheck"):
        """
        修改系统时间
        :param warehouse_number:
        :param picking_check:
        :param key:
        :return:
        """
        value = {
            "pickingCheck": picking_check,
            "packingCheck": 'true'
        }
        rec_id = self.wms_db.get_wh_config_date(warehouse_number=warehouse_number, key=key)
        self.batch.update_config(config_key=key, config_value=value, warehouse_number=warehouse_number, rec_id=rec_id)

    def add_sku(self, so_order_ids):
        """
        添加库存
        :param so_order_ids: so订单ID
        :return:
        """
        sku_info = self.wms_db.get_sku_by_order_ids(order_ids=so_order_ids)
        seen = set()
        filtered_sku_info = []
        for sku in sku_info:
            if sku['item_number'] not in seen:
                seen.add(sku['item_number'])
                filtered_sku_info.append(sku)
        if '8' in tuple(set(info['warehouse_number'] for info in filtered_sku_info)):
            raise Exception('当前脚本不支持仓库:8的作业模式')
        for item in filtered_sku_info:
            # 查询可用bin位
            location_inv_list = self.batch.get_location_inv_list(item_number=item['item_number'])
            available_stock_list_res = self.batch.get_available_bin_list(warehouse_number=item['warehouse_number'],
                                                                         storage_type=item['storage_type'],
                                                                         page_size=1)
            available_stock_list = jmespath(available_stock_list_res, 'body.data')
            # print(f"available_stock_list:{available_stock_list}")
            # sku_quantity = jmespath(location_inv_list, 'body.data[0].quantity')
            # print(f"sku_quantity:{sku_quantity}")
            # if sku_quantity > 0:
            #     self.batch.confirm_location_inv(location_no=available_stock_list[0]["bin_sn"],
            #                                     item_number=item['item_number'],
            #                                     adjust_number=0,
            #                                     warehouse_number=item['warehouse_number'])
            # 添加库存
            if available_stock_list:
                self.batch.confirm_location_inv(location_no=available_stock_list[0]["bin_sn"],
                                                item_number=item['item_number'],
                                                warehouse_number=item['warehouse_number'])
            else:
                print('无可用的Bin位')
        return sku_info

    def get_storage_type(self, item_number=None, storage_type=None):
        # 获取商品存储类型, item_number和storage_type不可以同时不传
        storage_type_mapping = {"dry": 1, "fresh": 2, "frozen": 3}
        if not storage_type:
            storage_type = wms.wms_db.get_item_info(item_number)["storage_type"]
        for k, v in storage_type_mapping.items():
            if storage_type == v:
                return k
            if str(storage_type).lower() == k:
                return v

    def get_inventory_batch_switch(self, warehouse_number, storage_type):
        # 获取当前仓库是否开启Batch
        if str(storage_type).isdigit():
            storage_type = self.get_storage_type(storage_type=int(storage_type))
        return wms.wms_db.select_wh_config_by_key(warehouse_number, "support_inventory_batch").get(storage_type, False)

    def receive_operation(self, warehouse_number, po_number, is_check=True):

        # 获取PO信息
        wms.central_receive.central_query_po(warehouse_number, po_number)

        # 获取PO单下的商品信息
        self.rep_info = {}
        result = wms.central_receive.receive_list(warehouse_number, po_number)
        if not result:
            return
        receive_list = [data['itemNo'] for data in result]  # 列表推导式
        print(f"========{receive_list}")
        self.rep_info['receive_list'] = receive_list
        print(f"========{self.rep_info['receive_list']}")

        # 扫描UPC/item number
        pieces_per_pack_list = []
        for item_number in self.rep_info['receive_list']:
            result = wms.central_receive.receive_detail(warehouse_number,
                                                        po_number,
                                                        item_number)
            pieces_per_pack_list.append(result['pieces_per_pack'])
        self.rep_info['pieces_per_pack_list'] = pieces_per_pack_list

        # 检查完成后点击 create&print task 生成 receive task

        # 获取调用接口之前该PO某商品下已生成的receive_task和status
        pre_total_task_no = wms.wms_db.get_receive_task_no(warehouse_number,
                                                           po_number)
        for index, item_number in enumerate(self.rep_info['receive_list']):
            item_info = wms.wms_db.get_item_info(item_number)
            expire_date = ""
            if item_info["check_shelf_life"] == 1:
                expire_date = wms.util.get_special_date(days=365)
            # central端——检查完成后点击 create&print task 生成receive task
            wms.central_receive.create_task(warehouse_number, po_number,
                                            item_number, self.rep_info['pieces_per_pack_list'][index], expire_date)

        # 获取调用接口之后该PO某商品下已生成的receive_task和status
        post_total_task_no = wms.wms_db.get_receive_task_no(warehouse_number, po_number)
        if is_check:
            pass
            # # 校验调用接口后生成的receive_task_no的条数
            # assert len(post_total_task_no) == len(pre_total_task_no) + len(
            #     self.rep_info['receive_list']), "生成receive_task 条数错误"
        # 校验调用接口后生成的receive_task_no的状态
        for data in post_total_task_no[0: len(self.rep_info['receive_list'])]:
            assert data['status'] == 10, "生成的receive_task_no 状态错误"

        # APP端——检查PO
        # 获取PO信息
        wms.central_receive.query_po(po_number, warehouse_number, 2)

        # APP端——获取PO单下的receive task 信息
        result = wms.central_receive.query_receive_task_list(po_number, 10, warehouse_number)
        if not result:
            return
        receive_task_no_list = {taskVoList['receive_task_no']: taskVoList['item_number'] for taskVoList in
                                result['taskVoList'] if taskVoList['receive_task_no']}  # 列表推导式
        self.rep_info['receive_list'] = receive_task_no_list
        self.rep_info["status"] = result['taskVoList'][0]["status"]
        # 添加receive task 的status状态校验（评审后待添加校验）
        assert result['taskVoList'][0]["status"] == 10, "生成的receive_task 状态错误"

        # APP端——扫描receive task
        self.rep_info['receive_task_number_item_number'] = {}
        for i in self.rep_info['receive_list']:  # 循环字典的键
            result = wms.central_receive.query_receive_task_list(po_number,
                                                                 self.rep_info['status'],
                                                                 warehouse_number, upc_code=i,
                                                                 flag=False)
            if not result:
                # 跳过本次循环进行下一次循环
                continue
            self.rep_info['receive_task_number_item_number'][i] = self.rep_info['receive_list'][i]

        # APP端——进入收货详情页面
        for i in self.rep_info['receive_task_number_item_number'].values():  # 取字典的值
            wms.central_receive.app_receive_detail(po_number, i)

        # APP端——输入收货数据点击confirm提交收货
        for receive_task_no, item_number in self.rep_info['receive_task_number_item_number'].items():  # 取字典的键值对
            # 收货前检查receive_quantity
            pre_receive_quantity = wms.wms_db.get_receive_quantity(po_number, item_number)

            # 收货前检查RECG0001好货区的库存——添加库存收货数据校验(用例评审后添加校验)
            pre_item_recg_inv = wms.wms_db.get_inventory_transaction(warehouse_number, 'RECG0001',
                                                                     item_number)

            # check收货数据
            wms.central_receive.central_receive_confirm(warehouse_number,
                                                        po_number,
                                                        item_number, receive_task_no)
            # 最终收货确认
            wms.central_receive.central_receive_confirm(warehouse_number,
                                                        po_number,
                                                        item_number, receive_task_no, only_check=False)

            # 收货后校验 receive_quantity是否增加正确
            post_receive_quantity = wms.wms_db.get_receive_quantity(po_number, item_number)
            assert post_receive_quantity['receive_quantity'] == pre_receive_quantity[
                'receive_quantity'] + 1, "receive_quantity记录错误"

            # 收货后校验收货batch是否正确添加
            check_receive_batch = wms.wms_db.get_receive_batch(receive_task_no, item_number)
            assert len(check_receive_batch) != 0, '收货 batch 未生成记录 ！'

            # 收货后校验 RECG0001好货区库存是否正确增加——添加库存收货数据校验(用例评审后添加校验)
            post_item_recg_inv = wms.wms_db.get_inventory_transaction(warehouse_number, 'RECG0001', item_number)
            assert post_item_recg_inv['quantity'] == pre_item_recg_inv['quantity'] + 1, "库存增加错误"
            print("收货完成")
        return list(self.rep_info['receive_task_number_item_number'].keys())

    def putaway_operation(self, putaway_warehouse_no, po_number, item_number, storage_type=1, type=0):
        '''
        type: 默认0为普通；1为as；2为geek

        '''

        # putaway——扫描pallet
        # 通过sql查询未被占用的pallet_no
        pallet_no = wms.wms_db.get_pallet_no(putaway_warehouse_no)["location_no"]

        # 检查pallet_no
        wms.put_away.check_Pallet(putaway_warehouse_no, pallet_no)
        print("扫描pallet")

        # putaway——扫描待上架的MLPN NO
        # 通过sql获取待上架的 mlpn
        lanel_no = wms.wms_db.get_mlpn_no(putaway_warehouse_no, po_number, item_number)['label_no']

        # 扫描待上架的MLPN NO
        result = wms.put_away.scan_And_Check_Task(putaway_warehouse_no, lanel_no,
                                                  storage_type)  # task_id = result[0]['task_id']
        putaway_task_id_list = []  # 定义空列表
        for i in result:
            task_id = i['task_id']
            putaway_task_id_list.append(task_id)
        print("扫描待上架的MLPN NO")

        # 点击confirm跳转到上架详情页面
        wms.put_away.confirm_And_Schedule_Tasks(putaway_warehouse_no, putaway_task_id_list, pallet_no)

        # AS商品——获取待上架mlpn的详细信息
        if type == 1:
            task_id_location_no = {}
            task_id_process = {}
            for task_id in putaway_task_id_list:
                result = wms.put_away.query_Putaway_Info(task_id, putaway_warehouse_no)
                process = result['process']
                if process == 1:
                    task_id_location_no[task_id] = "AUTOSTORE"
                    task_id_process[task_id] = process
            # 将as属性同步kpi
            result = wms.wms_db.get_as_attribute(item_number)
            if not result:
                # 调用同步kpi接口
                wms.put_away.update_items_as_attributes(putaway_warehouse_no, item_number)
        # 普通商品——获取待上架mlpn的详细信息
        elif type == 0:
            task_id_location_no = {}
            task_id_process = {}
            for task_id in putaway_task_id_list:
                result = wms.put_away.query_Putaway_Info(task_id, putaway_warehouse_no)
                print("**************")
                print(result)
                print("**************")
                recommend_location = None
                if result['recommend_location']:
                    recommend_location = result['recommend_location']
                else:
                    if not result['locationList']:
                        continue
                    # location_list = result['locationList']
                # process对应解释：1：OnlyBin、2： OnlyStock、3: random、5：D_RTV   "location_type": 4-bin,3-stock
                process = result['process']
                if process == 1:
                    location_no = None
                    if recommend_location:
                        location_no = recommend_location
                    else:
                        for location in result['locationList']:
                            if location['location_type'] == 4:
                                location_no = location['location_no']
                    if location_no:
                        task_id_location_no[task_id] = location_no
                    else:
                        continue
                elif process == 2:
                    location_no = None
                    if recommend_location:
                        location_no = recommend_location
                    else:
                        for location in result['locationList']:
                            if location['location_type'] == 3:
                                location_no = location['location_no']
                    if location_no:
                        task_id_location_no[task_id] = location_no
                    else:
                        continue
                elif process == 3:
                    task_id_location_no[task_id] = result['locationList'][0]['location_no']
                elif process == 5:
                    task_id_location_no[task_id] = 'D_RTV'
                else:
                    pass
                task_id_process[task_id] = process
            print("is_as is False")
        elif type == 2:
            # geek 逻辑
            task_id_location_no = {}
            task_id_process = {}
            for task_id in putaway_task_id_list:
                result = wms.put_away.query_Putaway_Info(task_id, putaway_warehouse_no)
                process = result['process']
                if process == 1:
                    task_id_location_no[task_id] = "GEEKPLUS"
                    task_id_process[task_id] = process
            # 将geek属性同步kpi
            result = wms.wms_db.get_geek_attribute(item_number)
            if not result:
                # 调用同步kpi接口
                wms.put_away.update_items_geek_attributes(putaway_warehouse_no, item_number)
        # 扫描要上架的库位（获取MLPN 下所有未上架的lpn）
        task_id_rec_id_lpn_no = {}  # {"task_id: [[rec_id, lpn_no], [rec_id, lpn_no]]"}
        for task_id, location_no in task_id_location_no.items():
            process = task_id_process[task_id]
            result = wms.put_away.query_Task_Confirm_Detail(task_id, location_no, putaway_warehouse_no, process)
            rec_id_lpn_no_list = []
            for data in result['lpnInfoList']:
                rec_id_lpn_no_list.append([data['rec_id'], data['lpn_no'], data['original_quantity']])
            task_id_rec_id_lpn_no[task_id] = rec_id_lpn_no_list

        # 点击confirm,提交上架数据
        print("*******************")
        for task_id, location_no in task_id_location_no.items():
            if task_id in task_id_rec_id_lpn_no:
                rec_id_lpn_no_list = task_id_rec_id_lpn_no[task_id]
                for rec_id_lpn_no in rec_id_lpn_no_list:
                    rec_id, lpn_no, original_quantity = rec_id_lpn_no[0], rec_id_lpn_no[1], rec_id_lpn_no[2]
                    wms.put_away.confirm_Putaway(
                        item_number,
                        location_no,
                        task_id,
                        putaway_warehouse_no,
                        rec_id,
                        lpn_no,
                        original_quantity,
                        original_quantity)

        return task_id_rec_id_lpn_no, pallet_no

    def labelreceive_operation(self, po_number, warehouse_number, userid):
        # 获取订单收货商品
        res = wms.label_receive.query_receive_list(po_number=po_number, status=1, pageSize=50,
                                                   warehouse_number=warehouse_number)
        itemquantity = len(jmespath(res, "poItemList"))
        for i in range(itemquantity):
            per_pack = jmespath(res, f"poItemList[{i}].pieces_per_pack")
            expired_dateunix = jmespath(res, f"poItemList[{i}].expired_dtm")
            item_number = jmespath(res, f"poItemList[{i}].item_number")
            # 收货详情
            res = wms.label_receive.label_receive_detail(po_number=po_number,
                                                         item_number=item_number,
                                                         expire_dtm=expired_dateunix, receive_dtm=None,
                                                         pieces_per_pack=per_pack)
            lpnquantity = jmespath(res, "lpnInfoList")
            lpnInfoList = []
            for j in range(len(lpnquantity)):
                lpnno = jmespath(res, f"lpnInfoList[{j}].lpn_no")
                original_quantity = jmespath(res, f"lpnInfoList[{j}].original_quantity")
                lpnInfoList.append({"lpn_no": lpnno, "original_quantity": original_quantity})
                # 扫描lpn
                wms.label_receive.label_receive_scan_lpn(warehouse_number=warehouse_number, lpn_no=lpnno,
                                                         in_user=userid, reference_no=po_number,
                                                         item_number=item_number)

            # 收货前检查RECG0001好货区的库存
            pre_item_inv = wms.wms_db.get_inventory_transaction(
                warehouse_number=warehouse_number, location_no="RECG0001",
                item_number=item_number,
            )
            rec_qty = jmespath(res, "po_quantity")
            expired_date = jmespath(res, "expire_dtm")
            # 收货确认
            resconfrim = wms.label_receive.label_receive_confirm(warehouse_number=warehouse_number,
                                                                 po_number=po_number,
                                                                 item_number=item_number, rec_qty=rec_qty,
                                                                 pieces_per_pack=per_pack, qc_ExpiredDate=expired_date,
                                                                 in_user=userid, lpnInfoList=lpnInfoList)
            rec_id = jmespath(resconfrim, "rec_id")
            wms.label_receive.label_receive_print_label(warehouse_number=warehouse_number, inbound_batch_id=rec_id,
                                                        in_user=userid)
            # 检查库存
            wms.wms_assert.check_non_batch_invenotry(
                warehouse_number=warehouse_number,
                location_no="RECG0001",
                item_number=item_number,
                adjust_qty=rec_qty,
                reference_no=po_number,
                inventory_type=[101],
                old_qty=pre_item_inv['quantity'],
                in_dtm=wms.util.get_current_time()
            )
            res = wms.label_receive.query_receive_list(po_number=po_number, status=1, pageSize=50,
                                                       warehouse_number=warehouse_number)
            if len(jmespath(res, "poItemList")) == 0:
                break
        # 查看入库单状态
        order_info = wms.wms_db.get_inbound_order_info(po_number)
        assert order_info["status"] == 60, "收货完成入库订单状态未变为60收货完成"


wms = WMS()
