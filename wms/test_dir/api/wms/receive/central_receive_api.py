# !/usr/bin/python3
# -*- coding: utf-8 -*-

import weeeTest

from wms.test_dir.api.wms.wms import header


class CentralReceiveAPI(weeeTest.TestCase):
    user = ''
    # 1、central receive
    # central端——检查po
    def central_query_po(self, warehouse_number, po_number):
        url = "/wms/receive/central/centralQueryPo"
        body = {
            "warehouse_number": warehouse_number,
            "po_number": po_number,
            "in_user": "jinyu.xu(9946725)"
        }
        self.post(url=url, headers=header, json=body)
        assert self.response["success"] == True, 'PO单无效，不能进行收货'

    # central端——获取PO单下的商品信息
    def receive_list(self, warehouse_number, po_number):
        url = "/wms/receive/central/centralQueryReceiveList"
        body = {
            "warehouse_number": warehouse_number,
            "po_number": po_number
        }
        self.post(url=url, headers=header, json=body)
        assert self.response["success"] == True, '获取PO单下的商品信息失败'
        return self.response["body"]

    # central端——扫描upc/item number
    def receive_detail(self, warehouse_number, po_number, item_number_or_upc):
        url = "/wms/receive/central/receiveDetail"
        body = {
            "warehouse_number": warehouse_number,
            "po_number": po_number,
            "item_number_or_upc": item_number_or_upc
        }
        self.post(url=url, headers=header, json=body)
        assert self.response["success"] == True, 'upc/item number不正确'
        return self.response["body"]

    # central端——检查完成后点击 create&print task 生成receive task
    def create_task(self, warehouse_number, po_number, item_number, pieces_per_pack, expire_date):
        url = "/wms/receive/central/centralReceiveCreateTask"
        if expire_date != "":
            qc_ExpiredDate = expire_date.replace("-","/") + " " + "00:00:00"
        else:
            qc_ExpiredDate = ""
        body = {
            "lp": "",
            "po_number": po_number,
            "item_number": item_number,
            "pieces_per_pack": pieces_per_pack,
            "weight_error": True,
            "image_error": False,
            "qc_ExpiredDate": qc_ExpiredDate,
            "ng_ExpiredDate": "",
            "rec_memo": "",
            "in_user": self.user,
            "warehouse_number": warehouse_number,
            "rec_Id": 0,
            "location_no": "",
            "images": [],
            "is_recount": 0,
            "temperature": "0.00F",
            "only_check_request": False,
            "input_dtm_str": expire_date,
            "input_dtm_type": 0
        }
        self.post(url=url, headers=header, json=body)
        assert self.response["success"] == True, '生成receive task失败'

    # 2、APP端receive

    # APP端——检查po信息
    def query_po(self, po_number, warehouse_number, status):
        url = "/wms/common/queryPO"
        body = {
            "po_number": po_number,
            "warehouse_number": warehouse_number,
            "status": status
        }
        self.post(url=url, headers=header, json=body)
        # assert self.response["success"] == True
        return self.response["body"]

    # APP端——获取PO单下的receive task 信息、扫描receive task
    def query_receive_task_list(self, reference_no, status, warehouse_number, upc_code=None, flag=True):
        print(f"==================={flag}=======================")
        url = "/wms/receive/central/queryReceiveTaskList"
        body = {
            "reference_no": reference_no,
            "status": status,
            "pageSize": 5,
            "startColumn": 0,
            "warehouse_number": warehouse_number
        }
        if flag:
            body["is_search_all"] = False
        else:
            body["is_search_all"] = True
            body["upc_code"] = upc_code
        print(f'**********{body}*********')
        self.post(url=url, headers=header, json=body)
        print(f"----------------{self.response['success']}---------------------")
        assert self.response["success"] == True, '获取PO单下的receive task 信息失败'
        return self.response["body"]

    #  APP端——进入收货详情页面

    def app_receive_detail(self, po_number, item_number):
        url = "/wms/receive/receiveDetail"
        body = {
            "po_number": po_number,
            "item_number": item_number
        }
        self.post(url=url, headers=header, json=body)
        assert self.response["success"] == True

    #  APP端——输入收货数据点击confirm提交收货
    def central_receive_confirm(self, warehouse_number,reference_no,item_number,receive_task_no,only_check=True):
        url = "/wms/receive/central/centralReceiveConfirm"
        body = {
            "warehouse_number": warehouse_number,
            "good_cases": 0,
            "good_qty": 1,
            "reference_no": reference_no,
            "item_number": item_number,
            "receive_task_no": receive_task_no,
            "in_user": self.user,
            "images": [],
            "only_check": only_check,
            "module_name": 'receive'
        }
        # only_check == False 才是最终交互，only_check == True只是检查数据
        if only_check == False:
            body["copies"] = 2
        self.post(url=url, headers=header, json=body)
        assert self.response["success"] == True, '点击confirm提交收货失败'


 #  APP端——错收冲减
    def delete_receive(self, warehouse_number, receive_task_no):
        url = "/wms/receive/central/resetTask"
        body = {
                  "warehouse_number": warehouse_number,
                  "receive_task_no": receive_task_no,
                  "in_user":self.user
                }
        self.post(url=url, headers=header, json=body)
        assert self.response["success"] == True, '错收冲减失败'

