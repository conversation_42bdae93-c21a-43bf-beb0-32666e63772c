import weeeTest
from wms.test_dir.api.wms.wms import header
import json


class CentralInventory(weeeTest.TestCase):
    """
    central inv 相关接口
    """

    def update_product_inv_status(self):
        """
        商品售卖状态刷新(计划售卖)
        """
        self.get(url='/central/inventory/job/update/product/inv/status', headers=header)
        assert self.response['result'] is True

    def update_overtime_payment_inv(self):
        """
        取消订单异常商品库存修复
        """
        self.get(url='/central/inventory/job/update/overtime/payment/inv/detail', headers=header)
        assert self.response['result'] is True

    def job_save_daily_inv_snapshot(self):
        """
        库存备份，仓库商品某一时刻库存快照
        """
        self.get(url='/central/inventory/job/save/daily/inventory/snapshot', headers=header)
        assert self.response['result'] is True

    def redis_detail_query(self, item_number):
        """
        redis库存查询
        """
        url = "/central/inventory/admin/redis/detail/query"
        data = json.dumps(item_number)
        self.post(url=url, headers=header, data=data)
        assert self.response['result'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def inv_redis_key_del(self, key_list):
        """
        redis库存删除
        """
        if not key_list or not isinstance(key_list, list):
            return
        url = "/central/inventory/admin/redis/run/del"
        data = json.dumps(key_list)
        self.post(url=url, headers=header, data=data)
        assert self.response['result'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"

    def fulfill_region_query(self, data):
        """
        Redis履约信息查询接口
        """
        url = "/central/inventory/admin/redis/run/get"
        body = [data]
        data = json.dumps(body)
        self.post(url=url, headers=header, data=data)
        assert self.response['result'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        assert self.response['object'] is not None, "没查到履约信息：" + data
        return self.response

    def priority_product(self, product_id):
        """
        更新商品售卖优先级
        """
        url = "/central/inventory/sync/sell/priority/product/id"
        body = [product_id]
        data = json.dumps(body)
        self.post(url=url, headers=header, data=data)
        assert self.response['result'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def query_product_local_inv(self, productIds, dates):
        """
        分页查询商品对应日期的售卖库存
        """
        url = "/central/inventory/query/product/local/inv"
        data = {
            "dates": dates,
            "page": 0,
            "pageSize": 100,
            "productIds": productIds,
            "return_sales_org": True,
            "startColumn": 0
        }
        self.post(url=url, headers=header, data=json.dumps(data))
        assert self.response['result'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def query_product_region_inv(self, sales_org_id, date, zipcode, product_id, deliveryQty=False, channelType="main"):
        """
        查询商品每日售卖库存
        deliveryQty: 是否查询履约库存
        """
        url = "/central/inventory/region/query"
        data = {
            "date": date,
            "zipcode": zipcode,
            "product_id": product_id,
            "deliveryQty": deliveryQty,
            "channelType": channelType
        }
        if sales_org_id:
            data["sales_org_id"] = sales_org_id
        self.post(url=url, headers=header, data=json.dumps([data]))
        assert self.response['result'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def query_product_sales_inv(self, sales_org_id, date, product_ids):
        """
        批量查询Product库存(替换V2接口)
        product_ids: 商品List,eg:[30702]
        """
        url = "/central/inventory/query/local/warehouse/sales/inv"
        data = {
            "sales_org_id": sales_org_id,
            "date": date,
            "product_ids": product_ids
        }
        self.post(url=url, headers=header, data=json.dumps(data))
        assert self.response['result'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def query_all_date_inv_v4(self, product_id, product_type="normal"):
        """
        查询商品的销售组织可售日期库存v4
        """
        url = "/central/inventory/query/all/date/v4"
        data = [
            {
                "product_id": product_id,
                "product_type": product_type
            }
        ]
        self.post(url=url, headers=header, data=json.dumps(data))
        assert self.response['result'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response
