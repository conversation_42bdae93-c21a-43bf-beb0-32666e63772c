import weeeTest
from wms.test_dir.api.wms.wms import header
import json
import time


class EcInventory(weeeTest.TestCase):
    """
    ec inv 相关接口
    """

    def ec_inventory_query_v5(self, date, zipcode, product_id):
        """
        V5库存查询接口
        """
        url = "/ec/inventory/query/v5"
        body = [
            {
                "date": date,
                "zipcode": zipcode,
                "product_id": product_id
            }
        ]
        data = json.dumps(body)
        self.post(url=url, headers=header, data=data)
        assert self.response['result'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def ec_inventory_query_v4(self, date, sales_org_id, product_id, product_type="normal"):
        """
        V4库存查询接口
        """
        url = "/ec/inventory/query/v4"
        body = [
            {
                "date": date,
                "product_id": product_id,
                "product_type": product_type,
                "sales_org_id": 3
            }
        ]
        data = json.dumps(body)
        self.post(url=url, headers=header, data=data)
        assert self.response['result'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def create_order(self, date, product_id, product_type, sales_org_id, zipcode):
        """
        库存下单接口
        """
        order_id = "autotest_" + str(int(round(time.time() * 1000)))
        url = "/ec/inventory/order/deduct/v5"
        body = [
            {
                "date": date,
                "inventory_mode": "available",
                "order_id": order_id,
                "product_id": product_id,
                "product_type": product_type,
                "qty": -2,
                "sales_org_id": sales_org_id,
                "type": 10,
                "zipcode": zipcode
            }
        ]
        data = json.dumps(body)
        self.post(url=url, headers=header, data=data)
        assert self.response['result'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        assert self.response['object']['failedProducts'] is None, "下单失败"
        return self.response

    def cancel_order_v5(self, date, order_id, product_id, product_type, sales_org_id, zipcode):
        """
        取消订单接口
        """
        url = "/ec/inventory/order/deduct/v5"
        body = [
            {
                "date": date,
                "inventory_mode": "available",
                "order_id": order_id,
                "product_id": product_id,
                "product_type": product_type,
                "qty": 2,
                "sales_org_id": sales_org_id,
                "type": 11,
                "zipcode": zipcode
            }
        ]
        data = json.dumps(body)
        self.post(url=url, headers=header, data=data)
        assert self.response['result'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        assert self.response['object']['failedProducts'] is None, "取消订单失败"
        return self.response

    def query_zipcode_region(self, zipcode: list):
        """
        查询zipcode所在的Region
        zipcode: list
        """
        url = "/ec/inventory/query/local/region"
        data = json.dumps(zipcode)
        self.post(url=url, headers=header, data=data)
        assert self.response['result'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def query_product_region(self, product_ids: list):
        """
        查询商品所有天所有履约地区的库存(包含履约)
        product_ids: list
        """
        url = "/ec/inventory/query/all/region/inv"
        data = json.dumps(product_ids)
        self.post(url=url, headers=header, data=data)
        assert self.response['result'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response
