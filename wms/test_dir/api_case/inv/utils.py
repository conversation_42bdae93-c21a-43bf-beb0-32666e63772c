import json
from jsonpath import jsonpath
from time import sleep
from weeeTest import jmespath
from wms.test_dir.api.inv.ec_inventory_api import EcInventory
from wms.test_dir.api.wms.wms import wms


class InvDataUtils(object):
    def get_priority_from_sales_models(self, sales_models, product_id, region_id, warehouse_number):
        """
        从 sales_models 中获取指定 region_id 和 warehouse_number 的 priority 值
        """
        for entry in sales_models:
            key, value = entry.split('->')
            if key == f"inv:fulfill:area:sku_{product_id}_regionId_{region_id}":
                try:
                    data = json.loads(value)
                    for item in data:
                        if item["warehouse_number"] == warehouse_number:
                            print(item["priority"])
                            return item["priority"]
                except (json.JSONDecodeError, KeyError, TypeError) as e:
                    print(f"解析 JSON 数据时出错: {e}")

    def assert_create_order_v5(self, date, product_id, product_type, sales_org_id, zipcode):
        # 查询下单日期的可售库存
        inv_res = wms.central_inv_api.query_product_region_inv(sales_org_id, date, zipcode, product_id, deliveryQty=True)
        product_inv = jsonpath(inv_res, "$.object[0].qty")
        assert product_inv > 1, f"销售组织:{sales_org_id},zipcode:{zipcode}下, product_id:{product_id}在date:{date}可售库存为{product_inv}"
        # 调用下单接口
        res = EcInventory().create_order(date, product_id, product_type, sales_org_id, zipcode)
        failed_products = jmespath(res, "object.failedProducts")
        order_id = jmespath(res, "object.successProducts[0].message_id")
        assert failed_products is None, f'下单失败"failedProducts":{failed_products}'
        # 动态轮询检查订单写入DB
        max_retries = 20
        retry_interval = 2  # 每次轮询间隔4秒
        for _ in range(max_retries):
            order_detail = wms.inv_db.query_order_detail(order_id)
            if order_detail:
                status = order_detail[0].get("status")
                if status == "P":
                    print(f"订单明细{order_id}写入DB成功，状态为P")
                    return order_id
            sleep(retry_interval)
        raise TimeoutError("订单明细写入超时")

    def assert_cancel_order_v5(self, date, order_id, product_id, product_type, sales_org_id, zipcode):
        # 取消订单
        # 调用取消订单接口
        res1 = EcInventory().cancel_order_v5(date, order_id, product_id, product_type, sales_org_id, zipcode)

        # 提取失败产品列表
        failed_products = jmespath(res1, "object.failedProducts")
        if failed_products is not None:
            raise ValueError(f"取消订单失败: {failed_products}")

        # 提取成功产品的 message_id
        success_products = jmespath(res1, "object.successProducts")
        if not success_products or "message_id" not in success_products[0]:
            raise KeyError("取消订单成功但未返回 message_id")
        order_id = success_products[0]["message_id"]

        # 动态轮询检查订单状态，取消订单MQ消息有延迟
        max_retries = 10
        retry_interval = 6  # 每次轮询间隔4秒
        for _ in range(max_retries):
            order_detail = wms.inv_db.query_order_detail(order_id)
            if not order_detail:
                raise ValueError(f"查询订单{order_id}明细结果为空")

            status = order_detail[0].get("status")
            if status == "X":
                print("订单状态已更新为X")
                return

            print(f"订单状态尚未更新为X，当前状态: {status}")
            sleep(retry_interval)
        raise TimeoutError("订单状态更新超时，未变为X")

    def assert_inventory_query_v5(self, date, zipcode, product_id, sales_warehouse_number, inv_qty, sales_model="inventory"):
        # inventory query v5接口
        v5_res = wms.ec_inv_api.ec_inventory_query_v5(date, zipcode, product_id)
        assert jsonpath(v5_res, "$.object[0].sales_model") == [sales_model], f"售卖模式不是{sales_model}"
        assert jsonpath(v5_res, "$.object[0].warehouse_number") == [sales_warehouse_number], f'期望售卖仓库为{sales_warehouse_number}, 实际为{v5_res}'
        assert jsonpath(v5_res, "$.object[0].qty") == [inv_qty], f'期望售卖数量为{inv_qty}, 实际为{v5_res}'
        if inv_qty > 0:
            assert jsonpath(v5_res, "$.object[0].is_sold_out") == [False], f'售卖数量为{inv_qty}, 但是售罄字段不是False'

    def assert_inventory_daily_sales(self, sales_org_id, date, zipcode, product_id):
        # 查询每日可售库存接口
        inv_res = wms.central_inv_api.query_product_region_inv(sales_org_id, date, zipcode, product_id)
        product_inv = jsonpath(inv_res, "$.object[0].qty")
        assert product_inv, f"销售组织:{sales_org_id},zipcode:{zipcode}下, product_id:{product_id}无可售库存"
        return product_inv[0]

    def del_inv_redis_key_del(self, product_id, warehouse_number):
        """
        拼接Redis key进行删除
        """
        # 登录
        wms.wms_login.common_login()
        data = wms.central_inv_api.redis_detail_query(item_number=product_id)
        key_list = []

        for sales_model in data['object']['salesModels']:
            try:
                # 检查 sales_model 是否为有效字符串
                if not isinstance(sales_model, str) or '->' not in sales_model:
                    raise ValueError("Invalid sales_model format. Expected a string containing '->'.")

                # 分割字符串并提取第二部分
                parts = sales_model.split('->')
                if len(parts) < 2 or not parts[1].strip():
                    raise ValueError("Invalid sales_model format. The part after '->' is missing or empty.")

                target_data = parts[1].strip()

                # 使用 jmespath 进行搜索
                # 确保 target_data 是合法的 JSON 格式
                try:
                    sales_info_list = json.loads(target_data)
                except (ValueError, TypeError) as e:
                    raise ValueError(f"Failed to parse data: {e}")

                # 遍历 sales_info_list 并检查每个元素
                for sales_info in sales_info_list:
                    if sales_info is not None and sales_info.get('product_id') == product_id and sales_info.get('warehouse_number') == str(warehouse_number):
                        key_list.append(sales_model.split('->')[0])

            except Exception as e:
                # 统一异常处理，记录错误信息
                print(f"Error processing sales_model: {e}")

        for product_inv in data['object']['productInvs']:
            # 解析键值对
            key, value = product_inv.split('->')
            if key.startswith(f'inv:inventory:warehouseSellable:sku_{product_id}_wh_{warehouse_number}'):
                key_list.append(key)

        print(key_list)
        # 删除 Redis key
        wms.central_inv_api.inv_redis_key_del(key_list)
