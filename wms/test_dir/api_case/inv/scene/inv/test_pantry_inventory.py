# !/usr/bin/python3
# -*- coding: utf-8 -*-

import weeeTest
from jsonpath import jsonpath
from wms.test_dir.api.wms.wms import wms


class TestPantryAPI(weeeTest.TestCase):
    def setup_class(self):
        # 登录
        self.user_id = wms.wms_login.common_login()[0]
        self.sales_org_id = 3
        self.zipcode = "77070"
        self.warehouse_number = "25"
        self.date = wms.util.get_special_date(days=3)
        # 调EC item查询pantry商品
        res = wms.ec_item_api.query_categroy_product(self.zipcode, self.date)
        self.product_id = jsonpath(res, "$.object.contents[0].data.id")

        self.available_qty = None
        if self.product_id:
            # 从V5接口获取库存
            v5_result = wms.ec_inv_api.ec_inventory_query_v5(self.date, self.zipcode, self.product_id[0])
            self.available_qty = jsonpath(v5_result, "$.object[0].qty")

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'INV', 'INV-PRD')
    def test_query_local_warehouse_sales_pantry_inv(self):
        """
        /central/inventory/query/local/warehouse/sales/inv(全量商品切pantry相关接口)
        """
        assert self.product_id, f"zipcode={self.zipcode},date={self.date}下未找到pantry商品"
        tpa_res1 = wms.central_inv_api.query_product_sales_inv(self.sales_org_id, self.date, self.product_id)
        qty1 = jsonpath(tpa_res1, "$.object[0].qty")
        assert qty1 == self.available_qty, f"销售组织:{self.sales_org_id},zipcode:{self.zipcode}下, product_id:{self.product_id}可售库存与V5接口返回不一致,V5:{self.available_qty}, 实际:{qty1}"

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'INV', 'INV-PRD')
    def test_query_local_pantry_inv(self):
        """
        /central/inventory/query/product/local/inv(指定商品切pantry相关接口)
        """
        assert self.product_id, f"zipcode={self.zipcode},date={self.date}下未找到pantry商品"
        # 查询仓库库存
        tpa_res2 = wms.central_inv_api.query_product_local_inv(self.product_id, [self.date])
        qty2 = jsonpath(tpa_res2,
                        f'$...inventory_list[?(@.warehouse_number == "{self.warehouse_number}")].available_qty')
        assert qty2 == self.available_qty, f"销售组织:{self.sales_org_id},zipcode:{self.zipcode}下, product_id:{self.product_id}可售库存与V5接口返回不一致,V5:{self.available_qty}, 实际:{qty2}"

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'INV', 'INV-PRD')
    def test_query_inv_pantry_v4(self):
        """
        /ec/inventory/query/v4接口查询pantry库存
        """
        assert self.product_id, f"zipcode={self.zipcode},date={self.date}下未找到pantry商品"
        # 查询仓库库存
        tpa_res3 = wms.ec_inv_api.ec_inventory_query_v4(self.date, self.sales_org_id, self.product_id[0])
        qty3 = jsonpath(tpa_res3, "$.object[0].qty")
        assert qty3 == self.available_qty, f"销售组织:{self.sales_org_id},zipcode:{self.zipcode}下, product_id:{self.product_id}可售库存与V5接口返回不一致,V5:{self.available_qty}, 实际:{qty3}"

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'INV', 'INV-PRD')
    def test_query_all_date_pantry_v4(self):
        """
        /central/inventory/query/all/date/v4接口查询pantry库存
        """
        assert self.product_id, f"zipcode={self.zipcode},date={self.date}下未找到pantry商品"
        # 查询仓库库存
        tpa_res4 = wms.central_inv_api.query_all_date_inv_v4(self.product_id[0])
        qty4 = jsonpath(tpa_res4, f'$.object.[?(@.sales_org_id == {self.sales_org_id})].date_sold_status[0].remaining_count')
        assert qty4 == self.available_qty, f"销售组织:{self.sales_org_id},zipcode:{self.zipcode}下, product_id:{self.product_id}可售库存与V5接口返回不一致,V5:{self.available_qty}, 实际:{qty4}"


if __name__ == "__main__":
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True, open=False)
