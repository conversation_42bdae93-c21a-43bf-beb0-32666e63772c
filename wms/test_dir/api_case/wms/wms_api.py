# !/usr/bin/python3
# -*- coding: utf-8 -*-
import json
import weeeTest
from wms.test_dir.api.wms.wms import header

class WmsAPI(weeeTest.TestCase):
    """
    WMS通用接口封装
    """
    def get_available_restock_pallet(self, warehouse_number):
        """
        查询可用restock pallet
        Args:
            warehouse_number: 仓库编号
        Returns:
            list: 可用pallet库位号列表
        """
        url = "https://api.tb1.sayweee.net/wms/location/query_location_info"
        body = {
            "warehouse_number": warehouse_number,
            "flag": "",
            "location_no": "",
            "location_types": [38],
            "storage_type": "",
            "restock_type": "",
            "size_id": "",
            "aisles": [],
            "bays": [],
            "locationAttributeCodes": [],
            "startColumn": 0,
            "pageSize": 15,
            "toteSizes": [],
            "tempZones": [],
            "levels": [],
            "positions": []
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"].get("list", []) 