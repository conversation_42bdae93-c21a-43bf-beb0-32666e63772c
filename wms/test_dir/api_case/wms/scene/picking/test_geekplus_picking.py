#!/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_normal_picking.py
@Description    :  Geekplus Picking 流程测试脚本
@CreateTime     :  2023/6/6 10:04
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/6/6 10:04
"""

import logging
import time,json
import weeeTest
from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms


class TestPicking(weeeTest.TestCase):

    def test_geekplus_picking(self,order_id=None):
        """
        【112957】Geekplus Picking 流程
        """
        warehouse_number = global_data.geekPlus_picking['warehouse_number']
        user_id, user_name = wms.wms_login.common_login()

        if order_id is None:
            order_id = 242523270
        order_info = self.get_order_info(order_id)
        if order_info['shipping_status'] == 25:
            pick_list_id = self.get_pick_list_id(order_id)
            task_details = self.get_task_details(pick_list_id)

            self.update_order_and_config(warehouse_number, order_info['delivery_dtm'])
            self.send_pick_list(pick_list_id=pick_list_id)

            pallet_no = self.get_pallet_no(warehouse_number)
            out_order_code = f"TB1_{pick_list_id}"

            self.bind_container(warehouse_number, user_id, out_order_code, pallet_no)
            feedback_data = self.prepare_feedback_data(user_id, warehouse_number, out_order_code, pallet_no,
                                                       task_details)
            self.send_feedback(feedback_data)

            self.restore_data(order_id, pick_list_id)
        else:
            logging.info(f"订单非GeekPlus订单,订单状态为：{order_info['shipping_status']}")

    def get_order_info(self, order_id):
        return wms.wms_db.get_order_info(order_id=order_id, info=['shipping_status', 'delivery_dtm'])

    def get_pick_list_id(self, order_id):
        task_info = wms.wms_db.get_order_as_task(order_id=order_id, info=["rec_id", "pick_list_rec_id"])
        return task_info[0]["pick_list_rec_id"]

    def get_task_details(self, pick_list_id):
        return wms.wms_db.get_order_as_task_detail(pick_list_id=pick_list_id,
                                                   info=['rec_id', 'item_number', 'batch_no', 'receive_dtm', 'expire_dtm', 'quantity'])

    def update_order_and_config(self, warehouse_number, delivery_dtm):
        before_delivery_dtm = delivery_dtm - 86400
        wms.wms_db.update_order(order_status=2, delivery_dtm=before_delivery_dtm, warehouse_number=warehouse_number)

        start_dtm = wms.util.utc_to_day(delivery_dtm)
        config_value = self.get_config_value(start_dtm)
        wms.update_central_config(warehouse_number=warehouse_number, config_key="support_automation",
                                  config_value=config_value)

    def get_config_value(self, start_dtm):
        data = {
            "active": True,
            "agingOrderMaxTime": 600,
            "oneItemOneOrderVolumeLimit": 1000,
            "oneItemVolumeMaxLimit": 2500,
            "oneItemVolumeMinLimit": 1,
            "pickCartConfigList": [
                {"count": 4, "size": 10, "weight": 10},
                {"count": 2, "size": 15, "weight": 15},
                {"size": 25, "count": 0, "weight": 25},
                {"size": 30, "count": 0, "weight": 30}
            ],
            "startDeliveryDate": start_dtm,
            "supplier": "GeekPlus",
            "sendDetailIdDeliveryDate": "2000-11-29",
            "supportOneItem": False,
            "oosLockInventory": True,
            "sendSkipWaveIds": [],
            "cancelOrderSetKpiComplete": False,
            "restockCallActive": True,
            "mailOrderSupportOneItem": True,
            "mailOrderOneItemOneOrderVolumeLimit": 1000,
            "asItemCanMarkCondition": {
                "max_sku_weight_lb": 10,
                "max_skuBox_weight_lb": 54,
                "max_skuBox_volume_in3": 1000
            }
        }
        return json.dumps(data)

    def send_pick_list(self,pick_list_id):
        wms.autostore_picking.geekPlusJob()
        time.sleep(5)
        # 获取拣选列表的状态
        resp4 = wms.wms_db.get_order_as_list(pick_list_id=pick_list_id, info=["rec_id", "status"])
        pick_list_status = resp4[0]["status"]

        #10 已发送
        assert pick_list_status == 10, "Pick List发送KPI失败"

    def get_pallet_no(self, warehouse_number):
        return wms.wms_db.get_wh_storage_location_info(
            warehouse=warehouse_number, location_type=6, flag=0, info="location_No"
        )

    def bind_container(self, warehouse_number, user_id, out_order_code, pallet_no):
        bind_container_data = {
            "body": {
                "warehouse_code": warehouse_number,
                "binding_list": [{
                    "id": 118,
                    "out_order_code": out_order_code,
                    "owner_code": "SayWeee",
                    "operate_time": 1736480158410,
                    "operator": user_id,
                    "container_code": pallet_no,
                    "wall_code": "YD3",
                    "workstation_no": "106",
                    "seeding_bin_code": "A1",
                    "status": 1
                }]
            },
            "header": {
                "interface_code": "feedback_bind_container",
                "user_id": "Weee",
                "user_key": "96e79218965eb72c92a549dd5a330112",
                "warehouse_code": warehouse_number
            }
        }
        wms.autostore_picking.geekPlusApi(data=bind_container_data)

    def prepare_feedback_data(self, user_id, warehouse_number, out_order_code, pallet_no, task_details):
        sku_list = self.create_sku_list(out_order_code, task_details, user_id, pallet_no)
        container_sku_list = self.create_container_sku_list(task_details, pallet_no)

        return {
            "body": {
                "size": 1,
                "order_amount": 1,
                "order_list": [{
                    "picker": user_id,
                    "id": 118199,
                    "out_order_code": out_order_code,
                    "status": 3,
                    "warehouse_code": warehouse_number,
                    "owner_code": "SayWeee",
                    "plan_sku_amount": 4,
                    "pickup_sku_amount": 4,
                    "pick_type": 1,
                    "sync_date": 1736495880231,
                    "finish_date": 1736495802879,
                    "container_amount": 1,
                    "sku_list": sku_list,
                    "container_list": [{
                        "picker": user_id,
                        "id": 118200,
                        "wall_code": "YD3",
                        "out_order_code": out_order_code,
                        "container_code": pallet_no,
                        "sku_amount": 4,
                        "sku_type_amount": 2,
                        "creation_date": 1736495847038,
                        "start_time": 1736490510111,
                        "complete_time": 1736495802990,
                        "sku_list": container_sku_list,
                        "container_amount": 1,
                        "container_type": 2,
                        "wave_code": "W20250110-00000002",
                        "pick_type": 0,
                        "wave_type": 20,
                        "workstation_no": "106",
                        "seeding_bin_code": "A1",
                        "pick_seeding_bin_no": "A1",
                        "double_pick_type": 1
                    }],
                    "sku_amount": 4,
                    "data_source_platform": 200,
                    "carrier_code": "SayWeee",
                    "carrier_name": "SayWeee",
                    "start_time": 1736490510116
                }]
            },
            "header": {
                "interface_code": "feedback_outbound_order",
                "user_id": "Weee",
                "user_key": "96e79218965eb72c92a549dd5a330112",
                "warehouse_code": warehouse_number
            }
        }

    def create_sku_list(self, out_order_code, task_details, user_id, pallet_no):
        sku_list = []
        for detail in task_details:
            sku_item = {
                "externalCode": out_order_code,
                "column": "id",
                "value": 215732,
                "sku_id": detail['item_number'],
                "packing_spec": "",
                "out_order_code": out_order_code,
                "item": 1,
                "sku_code": detail['item_number'],
                "plan_amount": detail['quantity'],
                "pickup_amount": detail['quantity'],
                "is_sequence_sku": 0,
                "sequence_no": "",
                "out_batch_code": detail['batch_no'],
                "production_date": 1751173200000,
                "expiration_date": detail['expire_dtm'] * 1000,
                "input_date": 1735189200000,
                "batch_property01": "1751173200000",
                "batch_property02": "1766725200000",
                "batch_property03": "241226000006",
                "batch_property06": "100",
                "batch_property10": "PA20241226000005",
                "batch_property11": "SayWeee",
                "owner_code": "SayWeee",
                "shelf_bin_list": [{
                    "quantity": detail['quantity'],
                    "shelf_code": "P00010",
                    "shelf_bin_code": "P00010B05A",
                    "operator": user_id,
                    "container_code": pallet_no,
                    "logic_area_code": "logic-P"
                }]
            }
            sku_list.append(sku_item)
        return sku_list

    def create_container_sku_list(self, task_details, pallet_no):
        container_sku_list = []
        for detail in task_details:
            container_sku_item = {
                "sku_id": detail['item_number'],
                "sku_code": detail['item_number'],
                "amount": detail['quantity'],
                "out_batch_code": detail['batch_no'],
                "expiration_date": detail['expire_dtm'] * 1000,
                "owner_code": "SayWeee",
                "container_code": pallet_no
            }
            container_sku_list.append(container_sku_item)
        return container_sku_list

    def send_feedback(self, feedback_data):
        time.sleep(5)
        wms.autostore_picking.geekPlusApi(data=feedback_data)

    def restore_data(self, order_id, pick_list_id):
        if order_id == 242523270:
            wms.wms_db.update_order_ship_status(order_id=order_id, ship_status=25)
            wms.wms_db.update_automation_pick_list(status=1, pick_list_id=pick_list_id)
            wms.wms_db.delete_wh_automation_pick_result(pick_list_id=pick_list_id)
            wms.wms_db.update_pick_list_id(pick_list_id=pick_list_id)


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', debug=True)

