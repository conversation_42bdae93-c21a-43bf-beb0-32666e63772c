#!/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  your.name
@Version        :  V1.0.0
------------------------------------
@File           :  test_common_pick_flow.py
@Description    :  通用拣货完整流程测试用例
@CreateTime     :  2024/8/20 10:00
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2024/8/20 10:00
"""
import weeeTest
from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms

class TestCommonPickFlow(weeeTest.TestCase):
    """通用拣货完整流程测试用例"""
    
    def setup_class(self):
        """测试前准备工作"""
        # 初始化测试参数
        self.rep_info = globals()
        self.rep_info['warehouse_number'] = global_data.vendor_order['warehouse_number']
        self.rep_info['storage_type'] = global_data.vendor_order['storage_type']
        
        # 登录系统
        self.rep_info['user_id'], self.rep_info['user_name'] = wms.wms_login.common_login()
        
        # 设置测试环境参数
        self.rep_info['equipment_code'] = "LM-RT-11"  # 设备编码
        self.rep_info['pallet_code'] = "PLT0689"  # 托盘编码
        self.rep_info['biz_type'] = 70  # 业务类型

    def teardown_class(self):
        """测试后清理工作"""
        # 释放资源，清理环境
        pass
        
    def _verify_task_status(self, task_id, expected_status):
        """验证任务状态"""
        task_info = wms.wms_db.get_pick_task_info(task_id)
        assert task_info['status'] == expected_status, f"任务状态验证失败，期望：{expected_status}, 实际：{task_info['status']}"

    def test_01_scan_equipment(self):
        """
        测试扫描设备功能
        """
        # 准备阶段 (Arrange)
        warehouse_number = self.rep_info['warehouse_number']
        storage_type = self.rep_info['storage_type']
        user_id = self.rep_info['user_id']
        equipment_code = self.rep_info['equipment_code']
        
        # 执行阶段 (Act)
        scan_equipment_resp = wms.common_pick.scan_Equipment(
            warehouseNumber=warehouse_number,
            storageType=storage_type,
            userId=user_id,
            Equipment_code=equipment_code
        )
        
        # 断言阶段 (Assert)
        assert scan_equipment_resp is not None, "扫描设备返回结果为空"
        assert "taskDispatchList" in scan_equipment_resp, "未返回任务分派列表"
        assert len(scan_equipment_resp["taskDispatchList"]) > 0, "任务分派列表为空"
        
        # 保存任务信息
        self.rep_info['task_rec_id'] = scan_equipment_resp["taskDispatchList"][0]["bizTaskId"]
        self.rep_info['location_no'] = scan_equipment_resp["taskDispatchList"][0]["locationNo"]

    def test_02_scan_pallet(self):
        """
        测试扫描托盘功能
        """
        # 准备阶段 (Arrange)
        biz_type = self.rep_info['biz_type']
        warehouse_number = self.rep_info['warehouse_number']
        storage_type = self.rep_info['storage_type']
        user_id = self.rep_info['user_id']
        pallet_code = self.rep_info['pallet_code']
        
        # 执行阶段 (Act)
        scan_pallet_resp = wms.common_pick.scan_pallet(
            bizType=biz_type,
            warehouseNumber=warehouse_number,
            storageType=storage_type,
            userId=user_id,
            pallet_code=pallet_code
        )
        
        # 断言阶段 (Assert)
        assert scan_pallet_resp is not None, "扫描托盘返回结果为空"
        assert "bizOrderId" in scan_pallet_resp, "未返回业务订单ID"
        
        # 保存业务订单ID
        self.rep_info['biz_order_id'] = scan_pallet_resp["bizOrderId"]

    def test_03_get_upc_pick_detail(self):
        """
        测试获取UPC拣货详情功能
        """
        # 准备阶段 (Arrange)
        warehouse_number = self.rep_info['warehouse_number']
        task_rec_id = self.rep_info['task_rec_id']
        location_no = self.rep_info['location_no']
        biz_type = self.rep_info['biz_type']
        biz_order_id = self.rep_info['biz_order_id']
        pallet_code = self.rep_info['pallet_code']
        user_id = self.rep_info['user_id']
        
        # 执行阶段 (Act)
        pick_detail_resp = wms.common_pick.get_upc_pick_detail(
            warehouseNumber=warehouse_number,
            taskRecId=task_rec_id,
            locationNo=location_no,
            bizType=biz_type,
            bizOrderId=biz_order_id,
            pallet_code=pallet_code,
            userId=user_id
        )
        
        # 断言阶段 (Assert)
        assert pick_detail_resp is not None, "获取UPC拣货详情返回结果为空"
        assert "itemNumber" in pick_detail_resp, "未返回商品编号"
        assert "recommendTotalQty" in pick_detail_resp, "未返回推荐拣货数量"
        
        # 保存商品信息和拣货数量
        self.rep_info['item_number'] = pick_detail_resp["itemNumber"]
        self.rep_info['picked_quantity'] = pick_detail_resp["recommendTotalQty"]

    def test_04_confirm_upc_pick(self):
        """
        测试确认UPC拣货功能
        """
        # 准备阶段 (Arrange)
        item_number = self.rep_info['item_number']
        picking_action = 0  # 正常拣货
        picked_quantity = self.rep_info['picked_quantity']
        warehouse_number = self.rep_info['warehouse_number']
        task_rec_id = self.rep_info['task_rec_id']
        location_no = self.rep_info['location_no']
        biz_type = self.rep_info['biz_type']
        biz_order_id = self.rep_info['biz_order_id']
        user_id = self.rep_info['user_id']
        pallet_code = self.rep_info['pallet_code']
        
        # 执行阶段 (Act)
        confirm_pick_resp = wms.common_pick.confirm_upc_pick(
            item_number=item_number,
            picking_action=picking_action,
            picked_quantity=picked_quantity,
            warehouse_number=warehouse_number,
            task_rec_id=task_rec_id,
            location_no=location_no,
            biz_type=biz_type,
            biz_order_id=biz_order_id,
            user_id=user_id,
            picking_pallet_no=pallet_code
        )
        
        # 断言阶段 (Assert)
        assert confirm_pick_resp is not None, "确认UPC拣货返回结果为空"
        
        # 验证数据库中的拣货任务状态
        self._verify_task_status(task_rec_id, 1)  # 假设1表示已确认状态

    def test_05_pick_complete(self):
        """
        测试拣货完成功能
        """
        # 准备阶段 (Arrange)
        warehouse_number = self.rep_info['warehouse_number']
        pallet_code = self.rep_info['pallet_code']
        storage_type = self.rep_info['storage_type']
        user_id = self.rep_info['user_id']
        biz_type = self.rep_info['biz_type']
        biz_order_id = self.rep_info['biz_order_id']
        
        # 执行阶段 (Act)
        complete_resp = wms.common_pick.pick_complete(
            warehouse_number=warehouse_number,
            picking_pallet_no=pallet_code,
            storage_type=storage_type,
            user_id=user_id,
            type=biz_type,
            biz_order_id=biz_order_id
        )
        
        # 断言阶段 (Assert)
        assert complete_resp is not None, "拣货完成返回结果为空"
        assert "slotNo" in complete_resp, "未返回货位编号"
        
        # 保存货位编号
        self.rep_info['slot_no'] = complete_resp["slotNo"]
        
        # 验证数据库中的拣货任务状态
        self._verify_task_status(self.rep_info['task_rec_id'], 2)  # 假设2表示已完成状态
        
        # 验证托盘与商品的绑定关系
        pallet_item = wms.wms_db.get_pallet_item_binding(self.rep_info['task_rec_id'])
        assert pallet_item['pallet_no'] == pallet_code, f"托盘绑定验证失败，期望：{pallet_code}，实际：{pallet_item['pallet_no']}"
        assert pallet_item['item_number'] == self.rep_info['item_number'], "商品编号绑定验证失败"
        assert pallet_item['quantity'] == self.rep_info['picked_quantity'], "拣货数量验证失败"

    def test_06_scan_slot(self):
        """
        测试扫描货位功能
        """
        # 准备阶段 (Arrange)
        slot_no = self.rep_info['slot_no']
        warehouse_number = self.rep_info['warehouse_number']
        storage_type = self.rep_info['storage_type']
        user_id = self.rep_info['user_id']
        biz_type = self.rep_info['biz_type']
        biz_order_id = self.rep_info['biz_order_id']
        pallet_code = self.rep_info['pallet_code']
        
        # 执行阶段 (Act)
        scan_slot_resp = wms.common_pick.scan_slot(
            slotNo=slot_no,
            warehouseNumber=warehouse_number,
            storageType=storage_type,
            userId=user_id,
            type=biz_type,
            bizOrderId=biz_order_id,
            pickingPalletNo=pallet_code
        )
        
        # 断言阶段 (Assert)
        assert scan_slot_resp is not None, "扫描货位返回结果为空"
        
        # 验证托盘状态
        pallet_status = wms.wms_db.get_pallet_status(pallet_code)
        assert pallet_status == 0, f"托盘状态验证失败，期望：0，实际：{pallet_status}"


if __name__ == '__main__':
    weeeTest.main(base_url="https://test-api.sayweee.net", debug=True, open=False,
                 case_list=['test_common_pick_flow.py::TestCommonPickFlow'])