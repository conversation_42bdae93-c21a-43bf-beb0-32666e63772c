# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  Author
@Version        :  V1.0.0
------------------------------------
@File           :  test_repack.py
@Description    :  
@CreateTime     :  2024/8/14 10:54
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2024/8/14 10:54
"""
import logging
import time
from linecache import updatecache

import weeeTest
from weeeTest import jmespath

from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms



class TestRepack(weeeTest.TestCase):

    def setup_class(self):
        self.warehouse_number = global_data.repack_picking['warehouse_number']
        self.storage_type = global_data.repack_picking['storage_type']
        self.user_id, self.user_name = wms.wms_login.common_login()
        self.current_date = wms.repack.get_current_date()


    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_repack_pick(self):
        """
        【112977】Repack Pick 流程
        """
        #创建拣货任务
        resp1 = wms.repack.RepackCreateTask(delivery_date= self.current_date,warehouse=self.warehouse_number)
        assert resp1['success'] == True,"任务创建失败"
        time.sleep(5)

        #获取task_id
        task_id = wms.wms_db.get_repack_data(warehouse=self.warehouse_number,delivery_date=self.current_date).get('task_id')

        #处理stock库存
        #加库存 适配动态数据


        #查看任务详情，删除指定Item，再添加
        resp5 = wms.repack.getRepackTaskDetail(warehouse=self.warehouse_number,task_id=task_id)
        ItemDetail = resp5['body']
        ItemRecid = [item for item in ItemDetail if item['item_number'] == "8568"]
        if ItemRecid != []:
            ItemRecid = ItemRecid[0]["rec_id"]
            resp6 = wms.repack.deleteRepackItem(ItemRecId=ItemRecid)
            if resp6['success'] == True:
                resp7 = wms.repack.addRepackItem(itemNumber="8568", piecesPerPack="21.00", targetItemNumber="16632",targetPiecesPerPack="4.00", targetQuantity="10", taskId=task_id,warehouse_number=self.warehouse_number)
                assert resp7['success'] == True,"Item存在且未拣货，删除后添加失败"
                time.sleep(10)

            else:
                #更新Item
                resp9 = wms.repack.updateRepackItem(warehouse_number=self.warehouse_number, task_id=task_id, rec_id=ItemRecid,quantity="10")
                assert resp9['success'] == True,"Item存在且已开始拣货，更新Item失败"
                time.sleep(10)

        else:
            resp8 = wms.repack.addRepackItem(itemNumber="8568", piecesPerPack="21.00", targetItemNumber="16632",targetPiecesPerPack="4.00",
                                             targetQuantity="10", taskId=task_id,warehouse_number=self.warehouse_number)
            assert resp8['success'] == True,"Item不存在，直接添加失败"
            time.sleep(10)

        # repack pick
        # 获取任务列表
        resp2 = wms.repack.pickListWitchLpn(action=0, warehouse=self.warehouse_number, palletno="", taskId=task_id,type=None)
        assert resp2['success'] == True,"获取任务列表失败"
        time.sleep(10)
        task_list = {"full_num":resp2['body']['full_num'],"ground_num":resp2['body']['ground_num'],"air_num":resp2['body']['air_num']}
        non_zero_values = {key:value for key, value in task_list.items() if value != 0}
        task_type = list(non_zero_values.keys())
        assert task_type != [], "无可领取的repack pick任务"

        #获取任务偶现失败，确认原因

        if task_type[0] == "full_num":
            type =1
        elif task_type[0] == "ground_num":
            type =2
        else:
            type =3
        #创建任务时，添加库存校验，逐步库存校验断言 pallet状态 库存变化


        # 查询可用的pallet
        palletno = wms.wms_db.get_wh_storage_location_info(warehouse=self.warehouse_number, location_type=38, flag=0,info="location_No")

        #领取任务 type 1-Full; 2-ground; 3-air;
        resp3 = wms.repack.pickListWitchLpn(action=1,warehouse=self.warehouse_number,palletno =palletno ,taskId = task_id,type= type)
        if resp3['success'] == True:
            task_location_no = resp3['body']['picking_list'][0]['source_location']
            type_upc_lpn = resp3['body']['picking_list'][0]['picking_way']
            picking_task_id = resp3['body']['picking_list'][0]['picking_task_id']
            pieces_per_pack = resp3['body']['picking_list'][0]['pieces_per_pack']
            item_number = resp3['body']['picking_list'][0]['item_number']
            print("任务领取成功,location_no:",task_location_no,"任务属性，LPN任务",type_upc_lpn)

            #参数命名规范

            #LPN流程
            if type_upc_lpn ==2:
                resp4 = wms.repack.pickingDetailWitchLpn(locationno=task_location_no,picking_task_id=picking_task_id,
                                                         warehouse=self.warehouse_number,new_lpn_no='')
                assert resp4['success'] == True,"LPN查询成功"
                pickedLpnCount = resp4['body']['pickedLpnCount']
                taskLpnCount = resp4['body']['taskLpnCount']

                allLpnList = resp4['body']['allLpnList']
                newLpnList = [lpn_no['lpn_no'] for lpn_no in allLpnList]

                NopickedLpnlist = [lpn_no['lpn_no'] for lpn_no in allLpnList if lpn_no['picked'] == False]
                pickedLpnlist = [lpn_no['lpn_no'] for lpn_no in allLpnList if lpn_no['picked'] == True]

                # 循环遍历NopickedLpnlist
                while pickedLpnCount < taskLpnCount:
                    # 从列表中获取当前的LPN
                    current_lpn = NopickedLpnlist[pickedLpnCount]
                    # 调用函数并传递当前的LPN
                    wms.repack.pickingDetailWitchLpn(locationno=task_location_no,picking_task_id=picking_task_id,warehouse=self.warehouse_number,
                                                     new_lpn_no=current_lpn,pickedLpnList=pickedLpnlist)
                    # 将当前使用的LPN添加到pickedLpnlist中
                    pickedLpnlist.append(current_lpn)
                    # 打印当前的计数
                    pickedLpnCount += 1
                    #print(pickedLpnCount)
                    print(f"Picked LPN Count: {pickedLpnCount}, Current LPN: {current_lpn}")

                #confirm
                wms.repack.pickingConfirmWitchLpn(warehouse=self.warehouse_number,locationno=task_location_no,PalletNo=palletno,
                                                  skuQuantity=taskLpnCount*pieces_per_pack,picking_task_id=picking_task_id,pickedlpnlist=pickedLpnlist)
                time.sleep(3)
                #check item status
                wms.wms_assert.check_repack_status(task_id=task_id,item_number=item_number,status=20),"已拣货repack Item任务状态错误"
                time.sleep(5)
                wms.wms_db.check_tote_status(tote_no=palletno,warehouse=self.warehouse_number,status=3)
                palletno_quantity = wms.wms_db.get_location_inventory(warehouse=self.warehouse_number,location_no=palletno,item_number=8568)[0]['quantity']
                assert palletno_quantity == 50,"拣货完成，Pallet_quantity异常"

            else:
                logging.info("upc流程")

        else:
            logging.info("任务失败原因:",resp3['body'])

        # 退登
        wms.logout(warehouse_number=self.warehouse_number, user_id=self.user_id, user_name=self.user_name)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_repack_receive(self):
        """
        【112979】Repack Receive流程
        """
        user_id, user_name = wms.wms_login.common_login()

        task_id = wms.wms_db.get_repack_data(warehouse=self.warehouse_number, delivery_date=self.current_date).get('task_id')

        # #获取待收货任务列表
        po_number = "R"+str(task_id)
        resp1 = wms.repack.queryReceiveList(po_number=po_number,status=0,pageSize=5,startColumn = 0,
                                            warehouse_number = self.warehouse_number,reference_type = 4)
        if resp1["success"] == True:
            item_number = resp1["body"]["poItemList"][0]["item_number"]
            po_quantity = resp1["body"]["poItemList"][0]["po_quantity"]
            quantity = resp1["body"]["poItemList"][0]["quantity"]
            pieces_per_pack = resp1["body"]["poItemList"][0]["pieces_per_pack"]

            #Item详情
            resp2 = wms.repack.queryReceiveList(po_number=po_number, status=0, pageSize=5, startColumn=0,
                                                warehouse_number=self.warehouse_number, reference_type=4,
                                                is_search_all=True, upc_code=item_number)
            assert resp2["success"] == True,"查询Item详情失败"


            #Item Batch
            resp3 = wms.repack.queryRepackPickBatchList(reference_no=po_number,item_number=item_number,
                                                        warehouse_number=self.warehouse_number,user_id=self.user_id)
            assert resp3["success"] == True,"查询Item Batch详情失败"
            batch_no = resp3['body'][0]["batch_no"]
            #initial_RECG_quantity = wms.wms_db.get_batch_inventory_transaction(warehouse_number=self.warehouse_number,location_no='RECG0001',item_number=item_number, batch_no=batch_no)

            #Confirm
            resp4 = wms.repack.receiveConfirm(lp = "",po_number = po_number,item_number=item_number,
                                              rec_box=0,rec_qty=po_quantity,pieces_per_pack=pieces_per_pack,
                                              in_user=self.user_id,warehouse_number =self.warehouse_number,location_no ="",
                                              module_name="repack_receive",qc_ExpiredDate="",batch_no =batch_no)
            #check received quantity
            wms.wms_assert.check_receive_quantity(reference_no=po_number,item_number=item_number,receive_quantity=(quantity+po_quantity)),"错误receive quantity"
            #收货完成，添加采购SKU及销售SKU的库存校验
            #update_RECG_quantity = wms.wms_db.get_batch_inventory_transaction(warehouse_number=self.warehouse_number,location_no='RECG0001',item_number=item_number,batch_no=batch_no)
            #assert update_RECG_quantity == (initial_RECG_quantity+po_quantity),"收货完成，RECG库存错误"
            #上架task添加成功校验，上架指定SKU

        else:
            print("不存在待收货repack任务")


    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_repack_putaway(self):
        """
        【112978】Repack Putaway 流程
        """
        user_id, user_name = wms.wms_login.common_login()

        #上架前更新同一账号下putaway任务完成
        wms.wms_db.update_putaway_task(status=50,user_id=user_id)

        task_id = wms.wms_db.get_repack_data(warehouse=self.warehouse_number, delivery_date=self.current_date).get('task_id')

        # #获取待上架任务列表
        po_number = "R" + str(task_id)
        resp1 = wms.repack.queryUndoTaskList(warehouse_number=self.warehouse_number,user_id=self.user_id,po_number=po_number)
        assert resp1["success"] == True,"获取待上架任务列表失败"

        #获取sku详情
        resp2 = wms.repack.todoList(warehouse_number=self.warehouse_number,po_number=po_number,user_id=self.user_id)
        assert resp2["success"] == True,"获取待上架SKU详情失败"

        time.sleep(3)
        upc_code = resp2['body'][0]['upc_code']
        label_no = resp2['body'][0]['label_no']
        quantity = resp2['body'][0]['quantity']
        item_task_id = resp2['body'][0]['task_id']
        item_no = resp2['body'][0]['item_number']

        # 查询可用的pallet
        palletno = wms.wms_db.get_wh_storage_location_info(warehouse=self.warehouse_number, location_type=38, flag=0,info="location_No")

        #扫描LPN
        resp3 = wms.repack.scanAndCheckTask(warehouse_number=self.warehouse_number,po_number=po_number,user_id=self.user_id,lpn_no=label_no)
        assert resp3["success"] == True,"扫描LPN异常"
        putaway_task_id = resp3['body'][0]['task_id']

        # #Confirm
        resp4 = wms.repack.confirmAndScheduleTasks(warehouse_number=self.warehouse_number,user_id=self.user_id,
                                                   putaway_task_id=putaway_task_id,pallet_no=palletno)
        assert resp4["success"] == True,"Confirm异常"

        #获取推荐库位
        resp5 = wms.repack.queryPutawayInfo(putaway_task_id=putaway_task_id,warehouse_number=self.warehouse_number,user_id=self.user_id)
        assert resp5["success"] == True,"获取推荐Bin位异常"
        recommend_location = resp5['body']['recommend_location']
        recommend_lpn_quantity = resp5['body']['recommend_lpn_quantity']
        total_lpn_quantity = resp5['body']['total_lpn_quantity']


        #获取任务详情及LPN
        resp6 = wms.repack.queryTaskConfirmDetail(putaway_task_id=putaway_task_id,location_no=recommend_location,
                                                  warehouse_number=self.warehouse_number,user_id=self.user_id)
        item_number = resp6['body']['item_number']
        recommend_lpn_quantity = resp6['body']['recommend_lpn_quantity']
        lpnInfo = resp6['body']['lpnInfoList']

        new_list_of_dicts = []
        for d in lpnInfo:
            # 从每个字典中取出需要的值
            rec_id = d.get('rec_id')
            warehouse_number = d.get('warehouse_number')
            #item_number = d.get('item_number')
            lpn_no = d.get('lpn_no')
            original_quantity = d.get('original_quantity')
            receive_dtm = d.get('receive_dtm')

            new_dict = {'rec_id': rec_id, 'warehouse_number': warehouse_number,"item_number":None,
                        "lpn_no":lpn_no,"original_quantity":original_quantity,"receive_dtm":receive_dtm}
            # 将新的字典添加到新的列表中
            new_list_of_dicts.append(new_dict)

        time.sleep(3)

        #Confirm
        resp7 = wms.repack.confirmPutaway(item_number=item_number,location_no=recommend_location,task_id=putaway_task_id,
                                          warehouse_number=self.warehouse_number,in_user=self.user_id,module_name="repack_putaway",
                                          lpnInfoList=new_list_of_dicts,actual_location_recommend_qty=recommend_lpn_quantity)
        # check item status
        wms.wms_assert.check_putaway_status(task_id=putaway_task_id,reference_no=po_number,item_number=item_no,status=50),"已上架的任务状态错误"




if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', debug=True)
