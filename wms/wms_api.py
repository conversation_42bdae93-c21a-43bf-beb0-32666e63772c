# !/usr/bin/python3
# -*- coding: utf-8 -*-
import json
import weeeTest
from wms.test_dir.api.wms.wms import header

class WmsAPI(weeeTest.TestCase):
    """
    WMS通用接口封装
    """
    def get_available_restock_pallet(self, warehouse_number, flag="", location_no="", location_types=[38], storage_type="", restock_type="", size_id="", aisles=None, bays=None, locationAttributeCodes=None, startColumn=0, pageSize=15, toteSizes=None, tempZones=None, levels=None, positions=None):
        """
        查询可用restock pallet
        Args:
            warehouse_number: 仓库编号
            其他参数可选，参考接口文档
        Returns:
            list: 可用pallet库位信息列表
        """
        url = "https://api.tb1.sayweee.net/wms/location/query_location_info"
        body = {
            "warehouse_number": warehouse_number,
            "flag": flag,
            "location_no": location_no,
            "location_types": location_types,
            "storage_type": storage_type,
            "restock_type": restock_type,
            "size_id": size_id,
            "aisles": aisles or [],
            "bays": bays or [],
            "locationAttributeCodes": locationAttributeCodes or [],
            "startColumn": startColumn,
            "pageSize": pageSize,
            "toteSizes": toteSizes or [],
            "tempZones": tempZones or [],
            "levels": levels or [],
            "positions": positions or []
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"].get("list", []) 