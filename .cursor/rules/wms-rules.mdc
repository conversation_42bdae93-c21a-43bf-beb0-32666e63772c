---
description: 
globs: 
alwaysApply: true
---

# WMS接口自动化测试规范

## 项目结构

测试代码应遵循以下目录结构：
```
wms/
├── test_dir/
│   ├── api/          # API接口封装
│   ├── api_case/     # 测试用例
│   │   └── wms/      
│   │       ├── scene/    # 业务场景测试
│   │       └── simple/   # 单接口测试
│   └── db_utils/     # 数据库操作工具
└── qa_config/        # 测试配置
```

## TDD测试编写规范

### 测试类结构

```python
class Test业务名称(weeeTest.TestCase):
    """业务功能测试类"""

    def setup_class(self):
        """测试前准备工作"""
        # 1. 登录系统
        # 2. 初始化测试参数
        # 3. 设置测试环境

    def teardown_class(self):
        """测试后清理工作"""
        # 释放资源，清理环境

    def _辅助方法(self):
        """封装重复操作为辅助方法"""
        pass
        
    def test_测试场景(self):
        """
        测试场景描述
        """
        # 准备阶段 (Arrange)
        # 执行阶段 (Act)
        # 断言阶段 (Assert)
```

### 测试方法规范

1. **方法命名**: `test_功能点_条件_期望结果`
2. **文档注释**: 每个测试方法必须有详细描述
3. **断言清晰**: 每个断言必须有失败消息
4. **流程清晰**: 遵循准备-执行-断言三段式结构

## 接口封装规范

接口应封装在独立的类中，示例：

```python
class 业务模块API:
    """业务模块API封装"""
    
    def __init__(self):
        """初始化参数"""
        pass
        
    def 接口方法(self, 参数1, 参数2):
        """
        接口功能描述
        
        Args:
            参数1: 参数描述
            参数2: 参数描述
            
        Returns:
            返回值描述
        """
        # 接口实现
```

## 参考示例

参考 [test_restock_optimized.py](mdc:wms/test_dir/api_case/wms/scene/restock/test_restock_optimized.py) 实现以下测试模式：

1. 环境准备与清理
2. 辅助方法拆分复杂测试步骤
3. 清晰的测试流程组织
4. 详细的状态验证和断言

## 断言规则

1. **单一职责**: 每个断言验证一个条件
2. **明确消息**: 断言失败时提供明确错误信息
3. **状态验证**: 操作后验证系统状态变化
4. **数据一致性**: 验证数据在各系统间一致

## 测试数据管理

1. 使用全局配置文件管理测试数据
2. 测试数据与测试代码分离
3. 敏感数据使用环境变量或配置文件

## 测试执行规范

```python
if __name__ == '__main__':
    weeeTest.main(base_url="环境URL", debug=True, open=False,
                 case_list=['测试文件.py::测试类::测试方法'])
```

